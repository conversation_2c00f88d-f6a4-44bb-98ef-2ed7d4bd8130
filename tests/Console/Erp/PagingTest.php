<?php

declare(strict_types=1);

namespace Console\Erp;

use App\Console\Erp\Connector\RestConnector;
use App\Model\Orm\Orm;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Tester\Assert;
use Tester\DomQuery;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../bootstrap-integration.php';

/**
 * @testCase
 */
final class PagingTest extends TestCase
{
	public function __construct(
	) {}

	public function testDefault(): void
	{
		$paging = RestConnector::getPagingForRequest(1);
		Assert::equal([
			['min' => 1, 'max' => 2]
		] , $paging);

		Assert::exception(
			fn() => RestConnector::getPagingForRequest(0),
			\LogicException::class
		);

		$paging = RestConnector::getPagingForRequest(10001);
		Assert::equal([
			['min' => 2, 'max' => 10002],
			['min' => 1, 'max' => 2],
		] , $paging);

		$paging = RestConnector::getPagingForRequest(25521);
		Assert::equal([
			['min' => 15522, 'max' => 25522],
			['min' => 5522, 'max' => 15522],
			['min' => 1, 'max' => 5522],
		] , $paging);
	}
}

(new PagingTest())->run();
