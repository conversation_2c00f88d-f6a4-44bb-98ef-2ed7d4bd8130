<?php

declare(strict_types=1);

namespace App\Tests\AdminModule\Presenters\Page;

use App\Model\Security\UserStorageSwitcher;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\RedirectResponse;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Nette\Security\SimpleIdentity;
use Nette\Security\User;
use Tester\Assert;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 */
final class PagePresenterTest extends TestCase
{
	const ROOT_PAGE_ID = 1;

	public function __construct(
		private readonly ContainerFactory $containerFactory,
	)
	{
	}

	public function testDefaultPageLoad(): void
	{
		$container = $this->containerFactory->createContainer();

		$user = $container->getByType(User::class);
		$adminAuthenticator = $container->getByName('adminAuthenticator');
		$user->setAuthenticator($adminAuthenticator);

		$userStorageSwitcher  = $container->getByType(UserStorageSwitcher::class);
		$userStorageSwitcher->switch($user, UserStorageSwitcher::SECTION_BACKEND);

		$user->login(new SimpleIdentity(10031, 'developer'));

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Page:Admin:Page');
		$presenter->autoCanonicalize = false;

		$request = new Request('Page:Admin:Page', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION, 'id' => self::ROOT_PAGE_ID]);
		$response = $presenter->run($request);

		Assert::type(TextResponse::class, $response);
		$html = (string) $response->getSource();

		$find = 'Obsah';
		Assert::true(str_contains($html, $find), sprintf('Page has toggle tab "content" \'%s\'', $find));
	}

}

(new PagePresenterTest($containerFactory))->run();
