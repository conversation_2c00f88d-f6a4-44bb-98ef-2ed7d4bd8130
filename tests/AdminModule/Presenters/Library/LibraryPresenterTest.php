<?php declare(strict_types = 1);

namespace App\Tests\AdminModule\Presenters\Library;

use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Security\UserStorageSwitcher;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Nette\Security\SimpleIdentity;
use Nette\Security\User;
use Tester\Assert;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 */
final class LibraryPresenterTest extends TestCase
{

	public function __construct(
		private readonly ContainerFactory $containerFactory,
	)
	{
	}

	public function testLoadRootPage(): void
	{
		$container = $this->containerFactory->createContainer();

		$user = $container->getByType(User::class);
		$adminAuthenticator = $container->getByName('adminAuthenticator');
		$user->setAuthenticator($adminAuthenticator);

		$userStorageSwitcher  = $container->getByType(UserStorageSwitcher::class);
		$userStorageSwitcher->switch($user, UserStorageSwitcher::SECTION_BACKEND);

		$user->login(new SimpleIdentity(10031, 'developer'));



		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Library');
		$presenter->autoCanonicalize = false;

		$request = new Request('Admin:Library', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION, 'id' => LibraryTree::ROOT_ID, 'thickbox' => 0]);
		$response = $presenter->run($request);

		Assert::type(TextResponse::class, $response);

		$html = (string) $response->getSource();

		$find = 'Knihovna obrázků';
		Assert::true(str_contains($html, $find), sprintf('Page has title \'%s\'', $find));
	}

}

(new LibraryPresenterTest($containerFactory))->run();
