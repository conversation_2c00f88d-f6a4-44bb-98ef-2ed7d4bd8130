<?php

declare(strict_types=1);

namespace AdminModule\Presenters\Sign;

use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\RedirectResponse;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Nette\Security\User;
use Tester\Assert;
use Tester\DomQuery;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 */
final class SignPresenterTest extends TestCase
{
	public function __construct(
		private readonly ContainerFactory $containerFactory,
	)
	{
	}

	public function testDefault(): void
	{
		$container = $this->containerFactory->createContainer();

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Sign');
		$presenter->autoCanonicalize = false;

		$request = new Request('Admin:Sign', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION]);
		$response = $presenter->run($request);
		Assert::type(TextResponse::class, $response);

		$html = (string) $response->getSource();
		$dom = @DomQuery::fromHtml($html); // @ - parser throws because it tries to parse &subset=latin in attribute as html entity :(

		Assert::true($dom->has('form.form-login'), 'Sign in page contains login form');
		Assert::true($dom->has('input[name="username"]'), 'login form contains username input');
		Assert::true($dom->has('input[name="password"]'), 'login form contains password input');
	}

	public function testUnsuccessfulSignIn(): void
	{
		$container = $this->containerFactory->createContainer();

		$user = $container->getByType(User::class);
		Assert::false($user->isLoggedIn(), 'user is logged out');

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Sign');
		$presenter->autoCanonicalize = false;

		$postData = [
			'_' . Presenter::SIGNAL_KEY => 'signInForm-form-submit',
			'username' => '<EMAIL>',
			'password' => 'wrongPassword:(',
		];

		$request = new Request('Admin:Sign', 'POST', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION], $postData);
		$response = $presenter->run($request);
		Assert::type(TextResponse::class, $response);

		Assert::false($user->isLoggedIn(), 'user is still logged out');

		$html = (string) $response->getSource();
		$dom = @DomQuery::fromHtml($html); // @ - parser throws because it tries to parse &subset=latin in attribute as html entity :(

		Assert::true($dom->has('.message--error'), 'Sign in page contains error message');
	}

	public function testSuccessfulSignIn(): void
	{
		$container = $this->containerFactory->createContainer();

		$user = $container->getByType(User::class);
		Assert::false($user->isLoggedIn(), 'user is logged out');

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Sign');
		$presenter->autoCanonicalize = false;

		$postData = [
			'_' . Presenter::SIGNAL_KEY => 'signInForm-form-submit',
			'username' => '<EMAIL>',
			'password' => '7357p455w0RD!',
		];

		$request = new Request('Admin:Sign', 'POST', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION], $postData);
		$response = $presenter->run($request);
		Assert::type(RedirectResponse::class, $response);

		Assert::true($user->isLoggedIn(), 'user is logged in');
		Assert::same(10031, $user->getId(), 'test user is logged in');
	}
}

(new SignPresenterTest($containerFactory))->run();
