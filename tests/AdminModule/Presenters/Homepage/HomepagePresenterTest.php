<?php

declare(strict_types=1);

namespace App\Tests\AdminModule\Presenters\Homepage;

use App\Model\Security\UserStorageSwitcher;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\RedirectResponse;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Nette\Security\SimpleIdentity;
use Nette\Security\User;
use Tester\Assert;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 */
final class HomepagePresenterTest extends TestCase
{
	public function __construct(
		private readonly ContainerFactory $containerFactory,
	)
	{
	}

	public function testUnauthenticated(): void
	{
		$container = $this->containerFactory->createContainer();

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Homepage');
		$presenter->autoCanonicalize = false;

		$request = new Request('Admin:Homepage', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION]);
		$response = $presenter->run($request);
		Assert::type(RedirectResponse::class, $response);
		Assert::match('%a%sign%a%', $response->getUrl());
	}

	public function testAuthenticated(): void
	{
		$container = $this->containerFactory->createContainer();

		$user = $container->getByType(User::class);
		$adminAuthenticator = $container->getByName('adminAuthenticator');
		$user->setAuthenticator($adminAuthenticator);

		$userStorageSwitcher  = $container->getByType(UserStorageSwitcher::class);
		$userStorageSwitcher->switch($user, UserStorageSwitcher::SECTION_BACKEND);

		$user->login(new SimpleIdentity(10031, 'developer'));

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Homepage');
		$presenter->autoCanonicalize = false;

		$request = new Request('Admin:Homepage', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION]);
		$response = $presenter->run($request);
		Assert::type(RedirectResponse::class, $response);
		Assert::match('%a%page%a%', $response->getUrl());
	}
}

(new HomepagePresenterTest($containerFactory))->run();
