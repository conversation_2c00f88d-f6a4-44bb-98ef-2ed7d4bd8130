parameters:
	level: 7
	paths:
		- app
#		- tests
	fileExtensions:
		- php
		- phpt
	ignoreErrors:
		- '#(Used c|C)onstant WWW_DIR not found.#'
		- '#(Used c|C)onstant APP_DIR not found.#'
		- '#(Used c|C)onstant FE_TEMPLATE_DIR not found.#'
		- '#(Used c|C)onstant RS_TEMPLATE_DIR not found.#'
		- '#(Used c|C)onstant IMAGES_DIR not found.#'
		- '#(Used c|C)onstant FILES_DIR not found.#'
		- '#(Used c|C)onstant TEMP_DIR not found.#'
		- '#(Used c|C)onstant LOG_DIR not found.#'
		- '#Cannot call method setTranslator\(\) on Nette.#'
		- '#Cannot call method add\(\) on Nette.#'
		- '#Access to an undefined property Nette\\ComponentModel\\IContainer::\$translator#'
		- '#Nette\\Application\\UI\\Component::redirect\(\) expects#'
		- '#Nette\\Application\\UI\\Component::link\(\) expects#'
		- '#Call to an undefined method Nette\\Application\\UI\\Template::setTranslator#'
		- '#^Parameter \#1 \$translator of method Ublaboo\\DataGrid\\DataGrid\:\:setTranslator\(\) expects Nette\\Localization\\ITranslator, App\\Model\\Translator given\.$#'

		# forms
		- '#Call to an undefined method Nette\\ComponentModel\\IComponent::add#'
		- '#Call to an undefined method Nette\\ComponentModel\\IComponent::set#'

		- '#Cannot call method render\(\) on Nette\\Application\\UI\\Template#'   #FIX
		- '#Cannot call method setFile\(\) on Nette\\Application\\UI\\Template#'  #FIX
		- '#Cannot call method addFunction\(\) on Nette\\Application\\UI\\Template#'  #FIX

		- message: '#.*#'
		  path: app/Model/Orm/Traits/HasParameters.php

	checkMissingCallableSignature: true
	checkMissingIterableValueType: false
	scanDirectories:
		- app
	checkTooWideReturnTypesInProtectedAndPublicMethods: true
	checkUninitializedProperties: false
