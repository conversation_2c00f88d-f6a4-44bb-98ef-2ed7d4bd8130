{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "baseUrl": ".", "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}