@import 'choices.js/public/assets/styles/choices';

.choices {
	width: 100%;
	margin-bottom: 0;

	&.is-open + .inp-text__holder,
	&.is-focused + .inp-text__holder {
		border-color: $colorPrimary;
	}
}

.choices__inner {
	min-height: 0;
	padding: 8px 5px 3px;
	border: 1px solid $colorBd;
	border-radius: $radius;
	background-color: $colorWhite;
	font-size: $fontSize;
	box-shadow: $shadow;

	&:disabled {
		background-color: $colorBg;
	}

	.is-open &,
	.is-focused &,
	.is-focused.is-open & {
		border-color: $colorPrimary;
	}
}

.choices__input {
	margin: 0;
	padding: 0 5px 6px;
	background-color: $colorWhite;
	font-size: $fontSize;
	line-height: 18px;
}

.choices[data-type*='select-multiple'] .choices__button,
.choices[data-type*='text'] .choices__button {
	border-left: 1px solid $colorBlack;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 352 512'%3E%3Cpath d='M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z'/%3E%3C/svg%3E%0A");
}

.choices__list--multiple .choices__item {
	margin-right: 5px;
	margin-bottom: 5px;
	padding: 0 10px;
	border: 1px solid #e4e4e4;
	border-radius: 4px;
	background-color: #e4e4e4;
	color: $colorBlack;
	line-height: 18px;
}

.choices__list--dropdown,
.choices__list[aria-expanded] {
	z-index: 10;
	background-color: $colorBgLighten;
}

.choices__list--multiple .choices__item.is-highlighted {
	border: 1px solid $colorPrimary;
	background-color: $colorPrimary;
	color: $colorWhite;
}

.choices__list--multiple:not(:empty) + .choices__input--cloned[placeholder] {
	width: 1ch;
	min-width: 1ch !important; // stylelint-disable-line declaration-no-important
	padding: 0;
	overflow: hidden;
}

.choices__list--multiple:not(:empty) + .choices__input--cloned[placeholder]::placeholder {
	color: transparent;
}

@media (min-width: 640px) {
	.choices__list--dropdown .choices__item--selectable,
	.choices__list[aria-expanded] .choices__item--selectable {
		border-top: 1px solid $colorBd;
	}
}

.choices__list--dropdown .choices__item--selectable.is-highlighted,
.choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
	background-color: $colorPrimary;
	color: $colorWhite;
}

.choices__list--dropdown .choices__item,
.choices__list[aria-expanded] .choices__item {
	padding: 9px 10px;
	font-size: 14px;
	line-height: 18px;
}
