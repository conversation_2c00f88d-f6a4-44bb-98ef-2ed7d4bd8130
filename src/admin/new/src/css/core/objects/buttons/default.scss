.btn {
	display: inline-block;
	vertical-align: middle;
	padding: 0;
	border: 0;
	background: none;
	text-decoration: none;
	cursor: pointer;
	&__text {
		position: relative;
		display: block;
		padding: 8px 15px;
		border: 1px solid transparent;
		border-radius: 2px;
		background-color: $colorPrimary;
		color: $colorWhite;
		font-weight: bold;
		font-size: 1rem;
		line-height: 20px;
		text-align: center;
		text-decoration: none;
		transition: background-color $t, border-color $t, color $t;
		&.item-icon {
			display: flex;
			text-align: left;
		}
	}

	// VARIANTs
	&--full {
		display: block;
		width: 100%;
	}

	&--grey &__text {
		background: $colorBg;
		color: $colorTextLight;
	}

	&--sm &__text {
		padding: 4px 10px;
		font-size: 12px;
	}

	&--tag &__text {
		padding: 1px 7px 0;
		border-radius: $radiusSm;
		font-size: $fontSizeXs;
		line-height: 19px;
		text-transform: uppercase;
	}

	// STATEs
	&:disabled,
	&.is-disabled {
		opacity: 0.5;
		pointer-events: none;
	}

	// HOVERs
	.hoverevents &:hover &__text {
		background-color: $colorText;
		color: $colorPrimary;
	}
	.hoverevents &--grey:hover &__text {
		background-color: $colorBg;
		color: $colorPrimary;
	}
	.hoverevents &--remove:hover &__text {
		background-color: $colorBg;
		color: $colorRed;
	}
}
