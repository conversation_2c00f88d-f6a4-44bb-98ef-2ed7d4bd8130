@import 'ublaboo-datagrid/assets/datagrid-spinners';
@import 'ublaboo-datagrid/assets/datagrid';

.datagrid {
	@import 'happy-inputs/src/happy';
	@import 'bootstrap-select/dist/css/bootstrap-select.min';
	@import 'bootstrap/dist/css/bootstrap.min';
	padding: 0;
	tbody {
		tr {
			cursor: pointer;
		}
	}
	// a:not(.btn) {
	// 	color: $colorHover;
	// 	.hoverevents &:hover {
	// 		color: $colorLink;
	// 	}
	// }
	&.datagrid {
		table tbody td {
			vertical-align: middle;
		}
		table tbody tr.row-disabled {
			background: $colorBg;
		}
		table th.col-checkbox,
		table td.col-checkbox {
			padding-right: 5px;
			padding-left: 5px;
		}
		.oversize-datagrid & {
			.table {
				position: relative;
				margin: -1px;
				margin-bottom: 0;
			}
			.table thead tr:not(.row-group-actions) {
				position: sticky;
				top: -1px;
				z-index: 15;
				+ tr:not(.row-group-actions) {
					z-index: 14;
				}
			}
			.table thead tr:last-child {
				top: 25px;
			}
			.table td.editing {
				padding-right: 0;
			}
			.table td.editing textarea {
				display: block; // neni nutne potreba
				width: 100%; // neni nutne potreba
				min-width: 160px;
			}
			.col-name {
				cursor: initial;
			}
			form {
				height: calc(100vh - 125px); // .header (50px) + .main__header (70px) + .scroll--horizontal height (5px)
				border: 1px solid #dee2e6;
				border-right: none;
				border-bottom: none;
			}
			.datagrid-toolbar {
				float: left;
				margin-top: 0;
			}
			.datagrid-toolbar > span {
				margin-left: 0.5em;
			}
			.datagrid-import-actions {
				display: inline-block;
				float: left;
				margin-left: 0.5em;
			}
			.datagrid-group-actions {
				float: right;
			}
		}
		.oversize-datagrid--configuration-order & {
			.datagrid-toolbar {
				display: inline-block;
				float: right;
				margin-top: 0.35em;
			}
		}
	}
	.dropdown-menu {
		li {
			margin: 0;
			padding: 0;
			background: none;
		}
	}
	// .form-inline {
	// 	.custom-select,
	// 	.input-group {
	// 		width: 100%;
	// 	}
	// }
	.btn-xs {
		padding: 1px 5px;
		border-radius: 3px;
		font-size: 12px;
		line-height: 1.5;
	}
	.btn-primary {
		border-color: $colorPrimary;
		background: $colorPrimary;
		.hoverevents &:hover {
			border-color: $colorBlack;
			background: $colorBlack;
			color: $colorWhite;
		}
	}
}
