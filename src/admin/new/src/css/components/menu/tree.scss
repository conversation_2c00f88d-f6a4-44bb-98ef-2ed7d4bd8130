$colorTreeBlue: #3cdbc0;
$colorTreeGrey: #666666;

.m-tree {
	padding-bottom: 50px;
	overflow: hidden;
	visibility: hidden;
	//overflow-x: auto;
	li {
		&::before {
			display: none;
		}
	}
}

// DEFAULT (from js)
.jstree ul,
.jstree li {
	display: block;
	margin: 0;
	padding: 0;
	list-style-type: none;
}

.jstree {
	li {
		display: block;
		min-width: 18px;
		min-height: 18px;
		margin-left: 18px;
		line-height: 18px;
		white-space: nowrap;
	}
	& > ul {
		& > li {
			margin-left: 0;
		}
	}
	ins {
		display: inline-block;
		vertical-align: middle;
		width: 18px;
		height: 24px;
		margin: 0;
		padding: 0;
		text-decoration: none;
	}
	a {
		display: inline-block;
		vertical-align: middle;
		height: 24px;
		margin: 0;
		padding: 4px 4px;
		color: $colorTreeGrey;
		line-height: 16px;
		white-space: nowrap;
		text-decoration: none;
		&:focus {
			outline: none;
		}
		& > ins {
			width: 16px;
			height: 16px;
		}
		& > .jstree-icon {
			margin-right: 3px;
		}
	}
}

.jstree-rtl {
	li {
		margin-right: 18px;
		margin-left: 0;
	}
	& > ul {
		& > li {
			margin-right: 0;
		}
	}
	a {
		& > .jstree-icon {
			margin-right: 0;
			margin-left: 3px;
		}
	}
}

li {
	&.jstree-open {
		& > ul {
			display: block;
		}
	}
	&.jstree-closed {
		& > ul {
			display: none;
		}
	}
}

// THEME
/* stylelint-disable-next-line no-duplicate-selectors */
.jstree {
	li,
	ins {
		background-color: transparent;
		background-image: url($imgPath+'ico/tree/d.png');
		background-repeat: no-repeat;
		background-size: auto;
		color: $colorTreeBlue;
	}
	/* stylelint-disable-next-line no-duplicate-selectors */
	li {
		background-position: -90px 1px;
		background-repeat: repeat-y;
		&.jstree-last {
			background: transparent;
		}
	}
	.jstree-open {
		& > ins {
			background-position: -72px -101px;
		}
	}
	.jstree-closed {
		& > ins {
			background-position: -54px -101px;
		}
	}
	.jstree-leaf {
		& > ins {
			background-position: -36px -101px;
		}
	}
	.jstree-hovered {
		background: lighten($colorTreeBlue, 35%);
		color: $colorTreeGrey;
	}
	.jstree-hovered:hover {
		color: $colorTreeGrey;
	}

	.jstree-clicked {
		background: $colorTreeBlue;
		color: $colorWhite;

		> ins {
			color: $colorWhite;
		}
	}
	.jstree-clicked:hover {
		color: $colorWhite;
	}
	/* stylelint-disable-next-line no-duplicate-selectors */
	a {
		// sk
		/* stylelint-disable-next-line no-duplicate-selectors */
		> .jstree-icon {
			position: relative;
			top: -1px;
			vertical-align: middle;
			margin-right: 5px;
			background: none;
			background-image: url($iconFolder);
		}

		&.jstree-loading {
			.jstree-icon {
				background: url($imgPath+'ico/tree/throbber.gif') center center no-repeat !important; /* stylelint-disable-line declaration-no-important */
			}
		}
		&.jstree-search {
			color: aqua;
		}
	}
	a.jstree-clicked > .jstree-icon {
		background-image: url($iconFolderWhite);
	}
	// sk
	/* stylelint-disable-next-line no-duplicate-selectors */
	.jstree-open {
		> a > ins {
			background-image: url($iconFolderOpen);
		}
		> a.jstree-clicked > ins {
			background-image: url($iconFolderOpenWhite);
		}
	}
	// sk
	/* stylelint-disable-next-line no-duplicate-selectors */
	.jstree-leaf {
		> a > ins {
			background-image: url($iconFile);
		}
		> a.jstree-clicked > ins {
			background-image: url($iconFileWhite);
		}
	}

	// Strom s ikonou složky v první úrovni
	&.one-level > ul > .jstree-leaf > a > ins {
		background-image: url($iconFolder);
	}
	&.one-level > ul > .jstree-leaf > a.jstree-clicked > ins {
		background-image: url($iconFolderWhite);
	}

	.jstree-no-dots {
		.jstree-open {
			& > ins {
				background-position: -18px 0;
			}
		}
		.jstree-closed {
			& > ins {
				background-position: 0 0;
			}
		}
	}
	.jstree-no-icons {
		a {
			.jstree-icon {
				display: none;
			}
		}
		.jstree-checkbox {
			display: inline-block;
		}
	}
	.jstree-search {
		font-style: italic;
	}
	.jstree-no-checkboxes {
		.jstree-checkbox {
			/* stylelint-disable-next-line declaration-no-important */
			display: none !important;
		}
	}
	.jstree-checked {
		& > a {
			& > .jstree-checkbox {
				background-position: -38px -19px;
				&:hover {
					background-position: -38px -37px;
				}
			}
		}
	}
	.jstree-unchecked {
		& > a {
			& > .jstree-checkbox {
				background-position: -2px -19px;
				&:hover {
					background-position: -2px -37px;
				}
			}
		}
	}

	.jstree-undetermined {
		& > a {
			& > .jstree-checkbox {
				background-position: -20px -19px;
				&:hover {
					background-position: -20px -37px;
				}
			}
		}
	}

	.jstree-locked {
		a {
			color: silver;
			cursor: default;
		}
	}

	.nopublic > ins {
		opacity: 0.2;
	}
	.jstree-clicked.nopublic > ins {
		opacity: 0.2;
	}
}

.jstree .jstree-no-dots li,
.jstree .jstree-no-dots .jstree-leaf > ins {
	background: transparent;
}

#vakata-dragged {
	&.jstree {
		ins {
			/* stylelint-disable-next-line declaration-no-important */
			background: transparent !important;
		}
		.jstree-ok {
			/* stylelint-disable-next-line declaration-no-important */
			background: url($imgPath+'ico/tree/d.png') -2px -53px no-repeat !important;
		}
		.jstree-invalid {
			/* stylelint-disable-next-line declaration-no-important */
			background: url($imgPath+'ico/tree/d.png') -18px -53px no-repeat !important;
		}
	}
}

#jstree-marker {
	&.jstree {
		/* stylelint-disable-next-line declaration-no-important */
		background: url($imgPath+'ico/tree/d.png') -41px -57px no-repeat !important;
		text-indent: -100px;
	}
}
#jstree-marker-line {
	pointer-events: none;
}

body {
	#vakata-contextmenu {
		padding: 5px;
		border: 1px solid $colorBd;
		border-radius: 3px;
		background: $colorWhite;
		box-shadow: 0 0 10px hexa($colorBlack, 0.15);
		li {
			background: transparent;
			zoom: 1;
			&::before {
				display: none;
			}
			&.vakata-separator {
				margin: 0;
				border-top: 1px solid $colorBg;
				background: white;
			}
			a {
				padding: 1px 10px;
				color: $colorTreeGrey;
				line-height: 20px;
			}
			ins {
				display: none;
			}
		}
		li a:hover,
		.vakata-hover > a {
			padding: 1px 10px;
			border: none;
			border-radius: 2px;
			background: lighten($colorTreeBlue, 80%);
			color: $colorTreeGrey;
		}
	}
}
