import './tools/svg4everybody';
import { Application } from 'stimulus';

import Alias from './components/Alias';
import Naja from './components/Naja';
import Toggle from './components/Toggle';
import ToggleCheckbox from './components/ToggleCheckbox';
import RemoveItem from './components/RemoveItem';
import ProductTitle from './components/ProductTitle';
import PageTitle from './components/PageTitle';
import ProductTitleSeo from './components/ProductTitleSeo';
import Tiny from './components/Tiny';
import SuggestInp from './components/SuggestInp';
import choices from './components/Choices';
import ProductVariant from './components/ProductVariant';
import ProductVariantEdit from './components/ProductVariantEdit';
import List from './components/List';
import Templates from './components/Templates';
import File from './components/File';
import ImageList from './components/ImageList';
import ImageLibrary from './components/ImageLibrary';
import CustomFields from './components/customFields/CustomFields';
import CustomField from './components/customFields/CustomField';
import CustomFieldList from './components/customFields/CustomFieldList';
import CustomFieldImage from './components/customFields/CustomFieldImage';
import CustomFieldFile from './components/customFields/CustomFieldFile';
import CustomFieldsCopy from './components/customFields/CustomFieldsCopy';
import Confirm from './components/Confirm';
import ModularContent from './components/ModularContent';
import DataGrid from './components/DataGrid';
import ModalClose from './components/ModalClose';
import ToggleAll from './components/ToggleAll';
import Tree from './components/Tree';
import MenuTree from './components/MenuTree';
import pagemenu from './components/pagemenu';
import icon from './components/icon';
import contenttoggle from './components/contenttoggle';
import LangToggles from './components/LangToggles';

const components = {
	Alias,
	Naja,
	Toggle,
	ToggleCheckbox,
	RemoveItem,
	ProductTitle,
	PageTitle,
	ProductTitleSeo,
	Tiny,
	SuggestInp,
	choices,
	ProductVariant,
	ProductVariantEdit,
	List,
	Templates,
	File,
	ImageList,
	ImageLibrary,
	CustomFields,
	CustomField,
	CustomFieldList,
	CustomFieldImage,
	CustomFieldFile,
	CustomFieldsCopy,
	Confirm,
	ModularContent,
	DataGrid,
	ModalClose,
	ToggleAll,
	Tree,
	MenuTree,
	pagemenu,
	icon,
	contenttoggle,
	LangToggles,
};

const App = {
	run(options) {
		const application = Application.start();

		Object.keys(components).forEach((component) => {
			if (component === 'Naja') {
				console.log(component);

				components[component].dataGridAjaxClass = options.dataGridAjaxClass || false;
			}

			application.register(component, components[component]);
		});
	},
};

window.App = App;
