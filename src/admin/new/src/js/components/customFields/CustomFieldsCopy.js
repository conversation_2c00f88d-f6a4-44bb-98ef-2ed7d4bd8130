import { ApplicationController } from 'stimulus-use';

export default class CustomFieldsCopy extends ApplicationController {
	static targets = ['select'];
	static values = {
		type: String,
		category: String,
		toLang: String,
	};

	copy() {
		if (this.hasSelectTarget) {
			const selectedLang = this.selectTarget.value;

			if (this.hasTypeValue && this.hasToLangValue) {
				this.dispatch('copyFromLanguage', {
					fromLang: selectedLang,
					toLang: this.toLangValue,
					type: this.typeValue,
					category: this.categoryValue,
				});
			}
		}
	}
}
