import { ApplicationController } from 'stimulus-use';
import Sortable from 'sortablejs';
import { list } from './templates/list';
import { text } from './templates/text';
import { checkbox } from './templates/checkbox';
import { listItem } from './templates/listItem';
import { select } from './templates/select';
import { image } from './templates/image';
import { file } from './templates/file';
import { message } from './templates/message';
import { radio } from './templates/radio';
import { arrayMove, hasSessionStorage } from '../../tools/utils';
import { dateTime } from './templates/dateTime';

export default class CustomFields extends ApplicationController {
	static targets = ['content', 'values'];
	static values = {
		scheme: Object,
		values: Object,
		lang: String,
		mutationid: String,
		uploadurl: String,
		defaultLangs: String,
		type: String,
		category: String,
	};

	state = {};

	groupSort = null;

	getSavedToggle() {
		if (hasSessionStorage && this.hasLangValue) {
			const savedToggles = sessionStorage.getItem('superadminToggles');
			if (!savedToggles) {
				return null;
			} else {
				const toggles = JSON.parse(savedToggles);
				return toggles && toggles.length ? toggles.find((item) => item.name === `lang-${this.langValue}`) || null : null;
			}
		} else {
			return null;
		}
	}

	connect() {
		const defaultLangs = this.hasDefaultLangsValue ? this.defaultLangsValue.split(' ') : [];
		const savedToggle = this.getSavedToggle();

		if (!this.hasDefaultLangsValue || (!savedToggle && defaultLangs.includes(this.langValue)) || (savedToggle && savedToggle.opened)) {
			this.init();
		}
	}

	init() {
		if (!this.schemeValue) {
			console.warn('No scheme defined for custom fields!');
			return;
		}

		this.state = {
			[this.langValue]: {
				id: this.langValue,
				key: this.langValue,
				parent: null,
			},
			...Object.keys(this.schemeValue).reduce((defaultValues, key) => {
				if (this.schemeValue[key].type === 'group') {
					this.setGroupState(this.langValue, key, this.schemeValue[key], defaultValues);
				} else {
					defaultValues[`${this.langValue}-${key}`] = {
						id: `${this.langValue}-${key}`,
						key,
						parent: this.langValue,
						order: this.schemeValue[key].order !== undefined ? this.schemeValue[key].order : 100,
						value: this.getDefaultValue(this.schemeValue[key].type, this.schemeValue[key].defaultValue),
						scheme:
							this.schemeValue[key].type === 'list' ? this.schemeValue[key] : this.getWrongTypeScheme(this.schemeValue[key]),
					};
				}

				return defaultValues;
			}, {}),
		};

		this.groupSort = new Sortable(this.contentTarget, {
			handle: '[data-drag-group-handle]',
			animation: 150,
			dataIdAttr: 'data-id',
			onUpdate: (event) => {
				this.updateGroupsOrder(event.item);
			},
		});

		try {
			this.updateStateFromValues();
			this.renderFields();
			this.generateValuesOutput();
			this.dispatch('updateSchemeValue', {
				scheme: this.schemeValue,
			});
		} catch (e) {
			console.warn('Error when trying to render CFs');
		}
		setTimeout(() => {
			if (this.hasContentTarget && this.contentTarget.children.length === 0 && Object.keys(this.valuesValue).length > 0) {
				alert('Z neznámého důvodu se nepodařilo načíst jazykově závislý obsah. Neukládejte prosím formulář.');
			}
		}, 0);
	}

	getWrongTypeScheme(scheme) {
		return {
			type: 'message',
			variant: 'error',
			label: `Pole "${scheme.label}" není typu "group" nebo "list" (typ pole: "${scheme.type}"). Pole na nejvyšší úrovní může být jen typu "group" nebo "list". Uprav pole tak, že bude potomkem jednoho z těchto typů.`,
		};
	}

	addToScheme(event) {
		const { scheme } = event.detail;
		this.schemeValue = {
			...this.schemeValue,
			...scheme,
		};
		this.state = {
			...this.state,
			...Object.keys(scheme).reduce((defaultValues, key, i) => {
				if (scheme[key].type === 'group') {
					this.setGroupState(this.langValue, key, scheme[key], defaultValues);
				} else {
					defaultValues[`${this.langValue}-${key}`] = {
						id: `${this.langValue}-${key}`,
						key,
						parent: this.langValue,
						order: i + 1,
						value: this.getDefaultValue(scheme[key].type, scheme[key].defaultValue),
						scheme: scheme[key],
					};
				}

				return defaultValues;
			}, {}),
		};

		Object.keys(scheme).forEach((id) => {
			this.renderField(this.contentTarget, this.state[`${this.langValue}-${id}`], this.state[`${this.langValue}-${id}`].scheme);
		});

		this.generateValuesOutput();
		this.generateRawValues();
		this.dispatch('updateSchemeValue', {
			scheme: this.schemeValue,
		});
	}

	removeFromScheme(event) {
		const listEl = event.target.closest('[data-customfieldlist-id-value]');

		if (!listEl) return;

		const stateKey = listEl.dataset.customfieldlistIdValue;
		const schemeKey = stateKey.split('-')[1];

		this.deleteFromState(stateKey);

		if (schemeKey) {
			const oldScheme = { ...this.schemeValue };
			delete oldScheme[schemeKey];
			this.schemeValue = oldScheme;
		}

		this.generateValuesOutput();
		this.generateRawValues();
		this.dispatch('updateSchemeValue', {
			scheme: this.schemeValue,
		});
	}

	setGroupState(parent, key, scheme, defaultValues) {
		const currentId = `${parent}-${key}`;
		defaultValues[currentId] = {
			id: currentId,
			key: key,
			parent,
			order: scheme.order !== undefined ? scheme.order : 100,
			scheme: scheme,
		};
		defaultValues[`${currentId}-0`] = {
			id: `${currentId}-0`,
			key: 0,
			parent: currentId,
			order: 1,
		};
		Object.keys(scheme.items).forEach((itemKey, i) => {
			if (scheme.items[itemKey].type === 'group') {
				this.setGroupState(`${currentId}-0`, itemKey, scheme.items[itemKey], defaultValues);
			} else {
				defaultValues[`${currentId}-0-${itemKey}`] = {
					id: `${currentId}-0-${itemKey}`,
					key: itemKey,
					parent: `${currentId}-0`,
					order: scheme.items[itemKey].order || i + 1,
					value: this.getDefaultValue(scheme.items[itemKey].type, scheme.items[itemKey].defaultValue),
					scheme: scheme.items[itemKey],
				};
			}
		});
	}

	getDefaultValue(type, defaultValue) {
		if (type === 'list') {
			return undefined;
		} else if (type === 'image' || type === 'file') {
			return defaultValue || [];
		} else if (type === 'suggest') {
			return defaultValue ? { value: defaultValue } : null;
		} else {
			return defaultValue || null;
		}
	}

	getListItemValue(type, value) {
		if (typeof value !== 'undefined') {
			return value;
		} else if (type === 'list' || type === 'image' || type === 'file' || type === 'group') {
			return [];
		} else {
			return null;
		}
	}

	addStateList(path, list, order, scheme) {
		const id = path.join('-');
		const key = path[path.length - 1];
		const parent = path.length === 1 ? null : path.slice(0, -1).join('-');

		if (!this.state[id]) {
			this.state[id] = {
				id,
				key,
				parent,
				order,
				scheme,
			};
		}
		if (scheme.type === 'group' && list.length === 0) {
			const listItem = Object.keys(scheme.items).reduce((itemValues, itemKey) => {
				itemValues[itemKey] = this.getListItemValue(scheme.items[itemKey].type, undefined);
				return itemValues;
			}, {});
			this.addStateListItem(path, id, listItem, 0, scheme);
		} else {
			list.forEach((item, i) => {
				this.addStateListItem(path, id, item, i, scheme);
			});
		}
	}

	addStateListItem(path, id, item, i, scheme) {
		this.state[`${id}-${i}`] = {
			id: `${id}-${i}`,
			key: i,
			parent: id,
			order: i + 1,
		};
		Object.keys(scheme.items).forEach((itemKey, j) => {
			const itemValue = this.getListItemValue(scheme.items[itemKey].type, item[itemKey]);
			this.addStateValue([...path, i, itemKey], itemValue, scheme.items[itemKey].order || j + 1, scheme.items[itemKey]);
		});
	}

	addStateValue(path, value, order, scheme) {
		const id = path.join('-');
		const key = path[path.length - 1];
		const parent = path.length === 1 ? null : path.slice(0, -1).join('-');

		if (scheme.type === 'list' || scheme.type === 'group') {
			this.addStateList(path, value, order, scheme);
		} else {
			if (!this.state[id]) {
				this.state[id] = {
					id,
					key,
					parent,
					order,
					value,
					scheme,
				};
			} else {
				this.state[id].value = value;
			}
		}
	}

	updateStateFromValues() {
		Object.keys(this.valuesValue).forEach((key, i) => {
			this.addStateValue([this.langValue, key], this.valuesValue[key], i + 1, this.schemeValue[key]);
		});
	}

	renderFields() {
		if (!this.hasContentTarget) {
			console.warn('No content target for CF!');
			return;
		}

		Object.keys(this.state)
			.filter((id) => this.state[id].parent === this.langValue)
			.map((id) => this.state[id])
			.sort(this.sortByOrder)
			.forEach((item) => {
				this.renderField(this.contentTarget, item, item.scheme);
			});
	}

	async renderList(parent, id, scheme) {
		const items = Object.keys(this.state).filter((itemKey) => this.state[itemKey].parent === id);
		const newParent = await list(parent, id, scheme, items.length);

		if (scheme.type === 'group' && this.state[id].parent === this.langValue && scheme.draggable) {
			//this.updateGroupsOrder(parent.querySelector('[data-list]'));
		}

		items.forEach(async (itemKey) => {
			this.renderListItem(newParent, itemKey, scheme);
		});
	}

	async renderListItem(parent, id, scheme) {
		const itemParent = await listItem(parent, id, scheme.type === 'group');
		const fields = Object.values(this.state).filter((fieldItem) => fieldItem.parent === id);
		fields.sort(this.sortByOrder).forEach((field) => {
			const idChunks = field.id.split('-');
			const schemeKey = idChunks[idChunks.length - 1];
			this.renderField(itemParent, field, scheme.items[schemeKey]);
		});
	}

	renderField(parent, field, scheme) {
		if (scheme.type === 'list' || scheme.type === 'group') {
			this.renderList(parent, field.id, scheme);
		} else {
			switch (scheme.type) {
				case 'text':
				case 'textarea':
				case 'tinymce':
				case 'suggest':
					text(parent, field, field.value || '', scheme, this.mutationidValue);
					break;

				case 'checkbox':
					checkbox(parent, field, field.value || false, scheme);
					break;

				case 'select':
					select(parent, field, field.value || '0', scheme);
					break;

				case 'radio':
					radio(parent, field, field.value || '', scheme);
					break;

				case 'image':
					image(parent, field, field.value || [], scheme);
					break;

				case 'file':
					file(parent, field, field.value || [], scheme, this.uploadurlValue);
					break;

				case 'message':
					message(parent, scheme);
					break;

				case 'dateTime':
					dateTime(parent, field, field.value || '', scheme);
					break;
			}
		}
	}

	updateValue(e) {
		const { value, key } = e.detail;

		if (this.state[key]) {
			this.state[key].value = value;
			this.generateValuesOutput();
			this.generateRawValues();
		}
	}

	generateValue(item, values) {
		if (item.scheme && (item.scheme.type === 'list' || item.scheme.type === 'group')) {
			this.generateList(item, values);
		} else {
			if (item.value === '' || item.value === null || (Array.isArray(item.value) && item.value.length === 0)) {
				return;
			}

			if (item.scheme.type === 'suggest' && typeof item.value === 'object') {
				values[item.key] = item.value.id;
			} else if (item.scheme.type === 'image') {
				if (item.scheme.multiple) {
					values[item.key] = item.value.map((img) => (typeof img === 'object' ? img.id : img));
				} else {
					values[item.key] = item.value.map((img) => (typeof img === 'object' ? img.id : img)).join('');
				}
			} else if (item.scheme.type === 'file') {
				if (item.scheme.multiple) {
					values[item.key] = item.value.map((file) =>
						typeof file.type === 'undefined' ? file : { id: file.id, name: file.name },
					);
				} else {
					values[item.key] =
						item.value.map((file) => (typeof file.type === 'undefined' ? file : { id: file.id, name: file.name }))[0] || '';
				}
			} else {
				values[item.key] = item.value;
			}
		}
	}

	generateList(item, values) {
		const listItems = Object.values(this.state)
			.filter((listItem) => listItem.parent === item.id)
			.sort(this.sortByOrder);

		if (listItems.length === 0) return;

		values[item.key] = [];
		listItems.forEach((listItem) => {
			const newListItem = {};
			values[item.key].push(newListItem);
			Object.values(this.state)
				.filter((valueItem) => valueItem.parent === listItem.id)
				.forEach((valueItem) => {
					this.generateValue(valueItem, newListItem);
				});
		});
	}

	generateValuesOutput() {
		const valuesOutput = Object.values(this.state)
			.filter((item) => item.parent === this.langValue)
			.reduce((newValues, valueItem) => {
				this.generateValue(valueItem, newValues);

				return newValues;
			}, {});

		if (this.hasValuesTarget) {
			this.valuesTarget.value = JSON.stringify(this.clearEmptyValues(valuesOutput));
		}
	}

	generateRawValue(item, values) {
		if (item.scheme && (item.scheme.type === 'list' || item.scheme.type === 'group')) {
			this.generateRawList(item, values);
		} else {
			if (item.value === '' || item.value === null || (Array.isArray(item.value) && item.value.length === 0)) {
				return;
			}

			values[item.key] = item.value;
		}
	}

	generateRawList(item, values) {
		const listItems = Object.values(this.state)
			.filter((listItem) => listItem.parent === item.id)
			.sort(this.sortByOrder);

		if (listItems.length === 0) return;

		values[item.key] = [];
		listItems.forEach((listItem) => {
			const newListItem = {};
			values[item.key].push(newListItem);
			Object.values(this.state)
				.filter((valueItem) => valueItem.parent === listItem.id)
				.forEach((valueItem) => {
					this.generateRawValue(valueItem, newListItem);
				});
		});
	}

	generateRawValues() {
		this.valuesValue = Object.values(this.state)
			.filter((item) => item.parent === this.langValue)
			.reduce((newValues, valueItem) => {
				this.generateRawValue(valueItem, newValues);

				return newValues;
			}, {});
	}

	shouldKeepEntry(value) {
		if (!Array.isArray(value)) return true;

		if (value.length === 0) return false;

		if (Object.keys(value[0]).length === 0 && value.length === 1) return false;

		return true;
	}

	clearEmptyValues(values) {
		const clearedValues = Object.keys(values).reduce((updated, key) => {
			if (Array.isArray(values[key])) {
				values[key] = values[key].reduce((reduced, value) => {
					if (typeof value === 'object' && Object.keys(value).length > 0) {
						reduced.push(this.clearEmptyValues(value));
					} else {
						reduced.push(value);
					}

					return reduced;
				}, []);
			}
			if (this.shouldKeepEntry(values[key])) {
				updated[key] = values[key];
			}
			return updated;
		}, {});
		return clearedValues;
	}

	addListItem(e) {
		const { id, order, scheme, parent } = e.detail;
		const item = Object.keys(scheme.items).reduce((itemValue, key) => {
			itemValue[key] = scheme.items[key].type === 'list' || scheme.items[key].type === 'group' ? [] : null;
			return itemValue;
		}, {});
		this.addStateListItem(id.split('-'), id, item, order, scheme);
		this.renderListItem(parent, `${id}-${order}`, scheme);
		this.generateValuesOutput();
		this.generateRawValues();
	}

	deleteFromState(id) {
		if (!this.state[id]) return;

		delete this.state[id];
		Object.values(this.state)
			.filter((item) => item.parent === id)
			.forEach((item) => {
				this.deleteFromState(item.id);
			});
	}

	removeListItem(e) {
		const { id } = e.detail;

		this.deleteFromState(id);
		this.generateValuesOutput();
		this.generateRawValues();
	}

	updateListOrder(e) {
		const { values } = e.detail;

		values.forEach((value) => {
			if (this.state[value.id]) {
				this.state[value.id].order = value.order;
			}
		});
		this.generateValuesOutput();
		this.generateRawValues();
	}

	updateGroupsOrder(draggedItem) {
		const siblings = Array.from(draggedItem.parentElement.children);
		const newScheme = { ...this.schemeValue };

		siblings.forEach((el, i) => {
			const id = el.dataset.customfieldlistIdValue;
			const stateItem = this.state[id];

			if (stateItem && newScheme[stateItem.key]) {
				newScheme[stateItem.key].order = i + 1;
			}
		});

		this.schemeValue = newScheme;
		this.dispatch('updateSchemeValue', {
			scheme: this.schemeValue,
		});
	}

	moveGroupUp(event) {
		const group = event.currentTarget.closest('[data-list]');
		const order = this.groupSort.toArray();
		const currentIndex = order.indexOf(group.dataset.id);
		const newIndex = currentIndex - 1;

		if (newIndex < 0) return;

		const newOrder = arrayMove(order, currentIndex, newIndex);

		this.groupSort.sort(newOrder, true);
		this.updateGroupsOrder(group);
	}

	moveGroupDown(event) {
		const group = event.currentTarget.closest('[data-list]');
		const order = this.groupSort.toArray();
		const currentIndex = order.indexOf(group.dataset.id);
		const newIndex = currentIndex + 1;

		if (newIndex >= order.length) return;

		const newOrder = arrayMove(order, currentIndex, newIndex);

		this.groupSort.sort(newOrder, true);
		this.updateGroupsOrder(group);
	}

	sortByOrder(a, b) {
		if (a.order < b.order) {
			return -1;
		}
		if (a.order > b.order) {
			return 1;
		}

		return 0;
	}

	langToggleSelected(event) {
		if (`lang-${this.langValue}` === event.detail['lang']) {
			if (event.detail['selected'] && this.contentTarget.childElementCount === 0) {
				this.init();
			}
		}
	}

	copyFromLanguage(event) {
		if (
			this.langValue === event.detail['toLang'] &&
			this.typeValue === event.detail['type'] &&
			this.categoryValue === event.detail['category']
		) {
			if (window.confirm('Kopírování přepíše veškerý obsah. Opravdu si přejete pokračovat?')) {
				const fromElement = document.querySelector(
					`[data-customfields-type-value="${this.typeValue}"][data-customfields-category-value="${this.categoryValue}"][data-customfields-lang-value="${event.detail['fromLang']}"]`,
				);

				if (this.hasContentTarget) {
					this.contentTarget.innerHTML = '';

					const modularMenu = this.element.querySelector('[data-modularcontent-target="menu"]');
					if (modularMenu) {
						modularMenu.innerHTML = '';
					}

					const scheme = fromElement.getAttribute('data-customfields-scheme-value');
					const values = fromElement.getAttribute('data-customfields-values-value');
					this.schemeValue = JSON.parse(scheme);
					this.valuesValue = JSON.parse(values);

					const controllersNames = this.element.getAttribute('data-controller');
					this.element.removeAttribute('data-controller');

					setTimeout(() => {
						this.element.setAttribute('data-controller', controllersNames);
					});
				}
			}
		}
	}
}
