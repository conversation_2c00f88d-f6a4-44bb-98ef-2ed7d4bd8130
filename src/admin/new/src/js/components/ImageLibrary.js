import $ from 'jquery';
import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';
import { nanoid } from 'nanoid';
import { handleSnippetResponse } from '../tools/utils';
import naja from 'naja';


export default class ImageLibrary extends Controller {
	static targets = ['content', 'img', 'list', 'tree', 'paging'];
	static values = {
		url: String,
		ids: Array,
		libraryVariant: String,
	};
	static selectedClass = 'b-imgs__item--selected';

	addCallback = null;
	removeCallback = null;
	multipleSelection = true;

	connect() {
		useDispatch(this);
		if (this.hasUrlValue && this.hasContentTarget) {
			fetch(this.urlValue)
				.then((response) => response.text())
				.then((result) => {
					this.contentTarget.innerHTML = result;
					naja.uiHandler.bindUI(this.contentTarget)
					setTimeout(() => {
						this.bindEvents();
						this.setSelectedImages();
						this.setLoading(false);
					}, 0);
				});
		}
		console.log('connect')

	}

	open(e) {
		const { variant, shouldOpen, addCallback, removeCallback, ids, multiple } = e.detail;

		this.libraryVariantValue = variant;

		if (shouldOpen) {
			this.element.classList.add('is-visible');
		}

		if (addCallback) {
			this.addCallback = addCallback;
		}

		if (removeCallback) {
			this.removeCallback = removeCallback;
		}

		if (typeof multiple !== 'undefined') {
			this.multipleSelection = multiple;
		} else {
			this.multipleSelection = true;
		}

		this.idsValue = ids || [];
		this.setSelectedImages();
	}

	close() {
		this.element.classList.remove('is-visible');
	}

	startLoading() {
		this.setLoading(true);
	}

	stopLoading() {
		this.setLoading(false);
	}

	setLoading(isLoading) {
		this.element.classList.toggle('is-loading', isLoading);
	}

	goTo() {
		this.setLoading(false);
		this.setSelectedImages();
		this.bindPagingEvents();
	}

	bindEvents() {
		this.bindPagingEvents();
	}

	bindPagingEvents() {
		if (this.hasPagingTarget) {
			this.addLinksListeners(this.pagingTarget);
		}
	}

	addLinksListeners(target) {
		const links = target.querySelectorAll('a');
		links.forEach((link) => {
			link.addEventListener('click', (event) => {
				event.preventDefault();
				this.setLoading(true);
				links.forEach((item) => item.classList.remove('is-selected'));
				link.classList.add('is-selected');

				$.ajax({
					url: link.href,
					type: 'GET',
				}).done((response) => {
					handleSnippetResponse(response);
					this.setLoading(false);
					this.setSelectedImages();
					this.bindPagingEvents();
				});
			});
		});
	}

	setSelectedImages() {
		if (this.hasImgTarget && this.hasIdsValue) {
			this.imgTargets.forEach((img) => {
				img.classList.remove(ImageLibrary.selectedClass);
				if (this.idsValue.includes(parseInt(img.dataset.id))) {
					img.classList.add(ImageLibrary.selectedClass);
				}
			});
		}
	}

	updateSelectedImages(e) {
		this.idsValue = e.detail.ids;
		this.setSelectedImages();
	}

	selectImage(e) {
		const imgTarget = e.target.closest('[data-imagelibrary-target="img"]');


		if (imgTarget) {
			const src = imgTarget.dataset.src;
			const nameEl = imgTarget.querySelector('.b-imgs__name');

			if (this.libraryVariantValue === 'product') {
				// handle image selection for product
				if (!imgTarget.dataset.id) return;
				const id = parseInt(imgTarget.dataset.id);

				if (this.idsValue.includes(id)) {
					this.idsValue = this.idsValue.filter((item) => id !== item);
					if (this.removeCallback) {
						this.removeCallback(id);
					}
				} else {
					this.idsValue = [...this.idsValue, id];
					if (this.addCallback) {
						this.addCallback(id, src, nameEl ? nameEl.textContent : '');
					}
				}

				this.setSelectedImages();

				// close if multiple selection is not allowed
				if (!this.multipleSelection) {
					this.close();
				}
			} else if (this.libraryVariantValue === 'tinymce' && this.addCallback) {
				// handle image selection for tinymce
				this.addCallback(src, { alt: nameEl ? nameEl.textContent : '' });
				this.close();
			}
		}
	}

	addImage(e) {
		const { url, folderId } = e.target.closest('button')?.dataset;
		if (e.target.files) {
			Array.from(e.target.files).forEach((file) => {
				const src = URL.createObjectURL(file);
				const uploadUrl = `${url}?id=${folderId}&ref=page&thickbox=1&do=upload`;

				this.dispatch('newItem', {
					name: 'libraryImage',
					// eslint-disable-next-line no-useless-escape
					id: `libraryImage_${nanoid().replace(/\-/gm, '_')}`,
					listTarget: this.listTarget,
					src,
					uploadUrl,
					file,
				});
			});
			// clear the file list
			e.target.type = 'text';
			e.target.type = 'file';
		}
	}
}
