import { Controller } from 'stimulus';




export default class Naja extends Controller {

	static dataGridAjaxClass = false;

	connect() {
		let currentTarget = null;

		if (!Naja.dataGridAjaxClass) {
			naja.uiHandler.selector = '[data-naja]';
		}

		naja.uiHandler.addEventListener('interaction', (event) => {
			// fix for aborting ajaxes (interaction will start afrter complete)
			setTimeout(() => {
				currentTarget = event.detail.element;

				// unification of current target
				if (!currentTarget.dataset.naja) {
					currentTarget = currentTarget.closest(naja.uiHandler.selector);
				}

				// add loading state
				if (currentTarget !== null) {
					if (currentTarget.dataset.najaLoader) {
						document.querySelector(currentTarget.dataset.najaLoader).classList.add('is-loading');
					} else {
						currentTarget.classList.add('is-loading');
					}
				}
			});
		});

		naja.addEventListener('complete', () => {
			// remove loading state
			if (currentTarget !== null) {

				if (currentTarget.dataset.najaLoader) {
					document.querySelector(currentTarget.dataset.najaLoader).classList.remove('is-loading');
				} else {
					currentTarget.classList.remove('is-loading');
				}

				if (currentTarget.dataset.najaOverlaySelector) {
					console.log(currentTarget.dataset.najaOverlaySelector)
					document.querySelector('#overlay-'+currentTarget.dataset.najaOverlaySelector).classList.add('is-visible')
					// document.querySelector(currentTarget.dataset.najaLoader)
				}

			}
			// reset currentTarget
			currentTarget = null;
		});

		naja.addEventListener('complete', (event) => {
			if (event?.detail?.payload?.newUrl) {
				naja.historyHandler.historyAdapter.pushState([], '', event.detail.payload.newUrl);
			}
		});

		naja.initialize();
	}
}
