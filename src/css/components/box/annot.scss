@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/functions';

.b-annot {
	$s: &;
	font-size: 2.4rem;
	&__title {
		margin: 0 0 2rem;
		text-wrap: pretty;
	}
	&__main {
		margin: 0 0 4rem;
	}
	&__img {
		margin: 0;
	}
	&__list {
		font-family: variables.$font-secondary;
		font-size: 1.6rem;
		line-height: calc(18 / 16);
	}
	&__btn {
		display: flex;
	}
	&__video {
		aspect-ratio: 16/9;
	}

	// MQ
	@media (config.$sm-down) {
		&__img,
		&__video {
			margin: 0 calc(variables.$row-main-gutter * -1);
		}
	}
	@media (config.$lg-up) {
		font-size: 3.2rem;
		line-height: calc(35.2 / 32);
		&__main {
			margin: 0 0 functions.spacing('lg');
		}
		&__list {
			margin-bottom: -4.4rem;
		}
		&__item {
			border-bottom-width: 4.4rem;
		}

		// MODIF
		&--article &__desc,
		&--career &__desc,
		&--showroom &__desc {
			font-size: 4.4rem;
			line-height: calc(48.4 / 44);
		}
		&--article {
			#{$s} {
				&__title {
					margin: 0 0 6.4rem;
				}
				&__main {
					margin: 0 0 functions.spacing('sm');
				}
			}
		}
		&--bz &__desc {
			font-size: 2.4rem;
			line-height: calc(30 / 24);
		}
	}
}
