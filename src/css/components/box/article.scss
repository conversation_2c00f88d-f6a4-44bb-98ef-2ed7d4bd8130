@use 'config';
@use 'base/variables';
@use 'base/functions';

.b-article {
	$s: &;
	> :last-child {
		margin-bottom: 0;
	}
	&__img {
		margin-bottom: 2.4rem;
	}
	&__category {
		margin: 0 0 functions.spacing('xxs');
	}
	&__category &__link {
		position: relative;
		z-index: 2;
		text-decoration: none;
	}
	&__title {
		margin: 0 0 2.4rem;
	}
	&__desc {
		margin: 0;
	}
	&__more {
		margin: 2rem 0 0;
		.btn {
			position: relative;
			z-index: 2;
		}
	}

	// MQ
	@media (config.$lg-up) {
		&__title {
			margin-bottom: 3.2rem;
		}

		// MODIF
		&--horizontal,
		&--horizontal-reversed {
			display: flex;
			flex-direction: row-reverse;
			#{$s}__content {
				width: percentage(calc(4 / 12));
				padding-right: 4rem;
			}
			#{$s}__img {
				width: percentage(calc(8 / 12));
				max-height: 60rem;
				margin-bottom: 0;
			}
		}
		&--md {
			#{$s}__title {
				margin: 0 0 1.8rem;
			}
			#{$s}__desc {
				font-size: 1.6rem;
			}
		}

		&--horizontal-reversed,
		.c-articles--about &--horizontal {
			flex-direction: row;
			#{$s}__content {
				padding-right: 0;
				padding-left: 2.4rem;
			}
		}
	}

	@media (config.$xl-up) {
		&__more {
			margin: 4.1rem 0 0;
		}

		// MODIF
		&--horizontal {
			#{$s}__desc {
				font-size: 1.6rem;
				line-height: 2.1rem;
			}
		}

		.c-articles--about & {
			#{$s}__desc {
				font-size: inherit;
				line-height: inherit;
			}
		}
	}
}
