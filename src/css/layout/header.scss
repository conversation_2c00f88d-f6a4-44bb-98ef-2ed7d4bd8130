@use 'config';
@use 'base/variables';
@use 'base/functions';

.header {
	$s: &;
	position: sticky;
	top: 0;
	z-index: 100;
	transition: background-color variables.$t, transform variables.$t;
	will-change: transform;
	.row-main {
		position: static;
	}
	&__inner {
		display: flex;
		justify-content: space-between;
		align-items: center;
		min-height: var(--header-height);
		padding: 1.2rem 0;
	}
	&__logo {
		flex: 0 0 auto;
		margin: 0;
		color: variables.$color-white;
	}
	&__link {
		display: flex;
		gap: 1rem;
		text-decoration: none;
	}
	&__bz {
		font-size: 1.6rem;
	}
	&__other {
		display: flex;
		gap: 1rem;
		flex: 0 0 auto;
		align-items: center;
	}

	// MODIF
	&--hp {
		color: variables.$color-white;
	}

	// STATES
	&.is-search-open,
	&.is-menu-open {
		background: variables.$color-white;
	}
	&.is-pinned,
	&.is-unpinned {
		background: variables.$color-white;
	}
	&.is-unpinned {
		transform: translateY(-100%);
	}

	// MQ
	@media (config.$xl-down) {
		&__menu {
			order: 1;
		}
		&__other {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			padding: 0 variables.$row-main-gutter;
			background: variables.$color-white;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
		}
		&__lang {
			min-width: 4.6rem;
		}

		// STATES
		&.is-menu-open &__other {
			visibility: visible;
			opacity: 1;
		}
	}
	@media (config.$xl-up) {
		&__menu,
		&__other {
			font-size: functions.rwd(1rem, 1.9rem);
		}
		&__other {
			gap: functions.rwd(false, 1rem);
		}

		// MODIF
		&--colorize:not(.is-menu-open):not(.is-search-open):not(.is-pinned):not(.is-unpinned) {
			.m-main__link,
			.b-login__link {
				color: variables.$color-white;
				text-decoration: none;
				text-underline-offset: 0.5rem;
				.hoverevents &:hover {
					text-decoration: underline;
				}
			}
			button {
				color: variables.$color-white;
			}
			.btn {
				--bg-color: transparent;
				--text-color: #{variables.$color-white};
				--bd-color: #{variables.$color-white};
				--hover-bg-color: #{variables.$color-white};
				--hover-bd-color: #{variables.$color-white};
				--hover-text-color: #{variables.$color-primary};
			}
			#{$s}__lang {
				a {
					color: variables.$color-primary;
				}
				.btn {
					--bd-color: #{variables.$color-white};
				}
			}
			#{$s}__link {
				color: variables.$color-white;
			}
			.m-main__list--sub {
				background: transparent;
			}

			// STATES
			a.is-active {
				text-decoration: underline;
			}
			.hoverevents & .m-lang__list a:hover {
				color: variables.$color-white;
			}
			.hoverevents & .m-lang:hover .btn {
				--bd-color: #{variables.$color-primary};
				--hover-bd-color: #{variables.$color-primary};
			}
		}
	}
}
