import { Controller } from '@hotwired/stimulus';
import { getOffsetTop } from '../tools/getOffsetTop.js';

export const create = () => {
	return class extends Controller {
		slideTo(event) {
			event.preventDefault();

			const sectionsHeight = document.querySelector('.m-sections') ? document.querySelector('.m-sections').clientHeight : 0;
			const el = this.element;
			const elId = el.getAttribute('href').replace('#', '');
			const elOffset = getOffsetTop(document.getElementById(elId));
			const extraOffset = 20 + sectionsHeight;

			this.windowScroll(elOffset - extraOffset);
		}
		slideToTop = () => {
			this.windowScroll();
		};
		windowScroll = (top = 0) => {
			window.scrollTo({
				behavior: 'smooth',
				top: top,
				left: 0,
			});
		};
	};
};
