import React from 'react';
import { Img } from '../common/components/Img';
import { Icon } from '../common/components/Icon';
import { formatPrice } from '../configurator/functions/formatPrice';
import { useAppState } from '../state';

type VariantItemProps = {
	variantNumber: string;
	title: string;
	image: string;
	price?: number;
	selected: boolean;
	link?: string;
};

export const VariantItem = ({ variantNumber, title, image, price, selected, link }: VariantItemProps) => {
	const [locale, currency, generalTranslations, showPrice] = useAppState((state) => [
		state.locale,
		state.currency,
		state.generalTranslations,
		state.showPrice,
	]);

	const content = (
		<label className="inp-variant inp-variant--lg">
			{selected && <input className="inp-variant__inp" type="radio" name="variant" value={variantNumber} defaultChecked={selected} />}
			<span className="inp-variant__inner">
				<span className="inp-variant__number">{variantNumber}</span>
				<span className="inp-variant__img">
					<Img src={image} />
				</span>
				<span className="inp-variant__name">{title}</span>
				{!!price && showPrice && <span className="inp-variant__info">od {formatPrice(price, locale, currency)}</span>}
				{/* TODO */}
				{/* <span className="inp-variant__info">
					<span className="item-icon u-c-yellow-dark">
						<Icon name="warning-triangle" className="item-icon__icon" />

						<span className="item-icon__text">pro dřevěné podlahy</span>
					</span>
				</span> */}
			</span>
			<span className="inp-variant__check">
				<span className="u-vhide">{generalTranslations['title_config_selected']}</span>
				<Icon name="check-rounded" />
			</span>
		</label>
	);

	return (
		<li className="grid__cell size--6-12">
			{selected ? (
				content
			) : (
				<a href={link} className="b-setup__item-link">
					{content}
				</a>
			)}
		</li>
	);
};
