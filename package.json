{"name": "ton", "version": "1.0.0", "sideEffects": false, "browserslist": ["since 2020", "not < 0.01%", "not QQAndroid > 1"], "engines": {"node": ">=16.14.0", "npm": ">=8.6.0"}, "devDependencies": {"@babel/core": "7.18.9", "@babel/plugin-transform-runtime": "7.18.9", "@babel/preset-env": "7.18.9", "@babel/preset-react": "7.23.3", "@babel/preset-typescript": "7.23.3", "@superkoders/eslint-config": "2.1.1", "@superkoders/prettier-config": "0.2.6", "@superkoders/stylelint-config": "2.1.0", "@types/qrcode": "1.5.5", "@types/react": "18.2.47", "@types/react-dom": "18.2.18", "ansi-colors": "4.1.3", "autoprefixer": "10.4.7", "babel-loader": "8.2.5", "babel-loader-exclude-node-modules-except": "1.2.1", "browser-sync": "2.27.10", "cheerio": "1.0.0-rc.12", "cypress": "13.8.0", "deepmerge": "4.2.2", "del": "6.1.1", "eslint": "8.20.0", "fancy-log": "2.0.0", "gulp": "4.0.2", "gulp-cheerio": "1.0.0", "gulp-consolidate": "^0.2.0", "gulp-format-html": "1.2.5", "gulp-imagemin": "8.0.0", "gulp-plumber": "^1.2.1", "gulp-postcss": "9.0.1", "gulp-rename": "2.0.0", "gulp-sass": "^5.1.0", "gulp-svgmin": "4.1.0", "gulp-svgstore": "9.0.0", "gulp-twing": "4.0.0", "gulp-w3c-html-validator": "5.1.3", "gulp-zip": "5.1.0", "husky": "7.0.4", "import-fresh": "3.3.0", "lint-staged": "12.3.7", "lodash": "4.17.21", "node-notifier": "10.0.1", "parse-sass-value": "2.3.0", "postcss": "8.4.14", "sass": "1.53.0", "through2": "4.0.2", "twing": "5.1.1", "typescript": "5.3.3", "vinyl": "2.2.1", "webpack": "5.73.0"}, "dependencies": {"@floating-ui/dom": "1.5.3", "@hotwired/stimulus": "3.0.1", "@sentry/react": "7.108.0", "@superkoders/cookie": "2.4.1", "@superkoders/modal": "1.7.0", "@superkoders/sk-tools": "1.8.1", "@vimeo/player": "2.20.1", "clsx": "2.1.0", "embla-carousel": "8.0.0-rc07", "ismobilejs": "1.1.1", "libphonenumber-js": "1.10.8", "lite-vimeo-embed": "0.3.0", "lite-youtube-embed": "0.3.3", "masonry-layout": "4.2.2", "naja": "2.4.0", "nanoid": "5.0.4", "nouislider": "15.7.1", "qrcode": "1.5.3", "react": "18.2.0", "react-dom": "18.2.0", "react-use-clipboard": "1.0.9", "scroll-lock": "2.1.5", "stimulus-use": "0.52.0", "usehooks-ts": "2.9.2", "wnumb": "1.2.0", "yt-player": "3.6.1", "zustand": "4.4.7"}, "scripts": {"changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "preversion": "npm run build", "version": "npm run changelog && git add -A", "postversion": "git push --follow-tags", "export": "npx gulp export", "build": "npx gulp min", "dev": "npx gulp", "prestart": "npm install", "preinstall": "npx check-engine", "start": "npm run dev", "lint:css": "stylelint \"src/**/*.{css,scss}\" || exit 0", "lint:css:fix": "prettier --write \"src/**/*.{css,scss}\" && stylelint --fix \"src/**/*.{css,scss}\"", "lint:js": "eslint . || exit 0", "lint:js:fix": "eslint . --fix", "print-version": "echo $npm_package_version", "snippet:build": "npx gulp build", "prepare": "husky install", "e2e": "npx cypress run --spec \"cypress/e2e/**/*\""}, "volta": {"node": "18.19.1", "npm": "8.6.0"}}