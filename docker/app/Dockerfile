FROM php:8.2-apache

COPY --from=mlocati/php-extension-installer /usr/bin/install-php-extensions /usr/local/bin
RUN IPE_GD_WITHOUTAVIF=1 install-php-extensions gd intl curl mbstring exif mysqli xdebug zip redis
# ^ use gd without avif, it currently takes ages to build (see https://github.com/mlocati/docker-php-extension-installer/issues/514)

# Install packages
RUN apt-get update && \
    apt-get install -y \
        supervisor

ENV APACHE_DOCUMENT_ROOT /var/www/html/www
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf
RUN echo "memory_limit = 1G" >> /usr/local/etc/php/conf.d/memlimit.ini

RUN echo "ServerName ton.superkoders.test" >> /etc/apache2/apache2.conf

COPY ./docker/app/supervisorctl-worker /etc/supervisor/conf.d/dev.conf

RUN a2enmod rewrite
