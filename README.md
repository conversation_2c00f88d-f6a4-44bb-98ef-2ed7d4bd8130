# Superadmin2019

### Prerequisites

The things you need to install Superadmin2019

```
php >=8.2
volta.sh

FE:
node 16.14.0
npm	8.6.0

BE:
node 14.19.0
npm	6.9.0
gulp 4.0.2
```

### Installing

####Backend (for mac)

1. create new project in MAMP: superadmin2019.yourname.l, allow ssl,
	direct the project to a directory /htdocs/superadmin2019/www,
	test in browser:  https://superadmin2019.jmeno.l/
    (if there is error 500, this is ok :-))

2. to console enter: composer install
(if you don't have the composer, enter php composer.phar install, or /usr/bin/php composer.phar install)
(make sure your php command is in right version, try: php -v, if is not, set in mamp)


3. create new database, with name: superadmin2019, codding utf-8, czech (localhost/adminer.php)

4. config.local.neon - create this file from template config.local.neon.tmp (placed in /app/config/...)
    -fill your access to database: root, root, ....
    -set domainUrl to: superadmin2019.yourname.l
    -set lang doman to: superadmin2019.yourname.l

Debuging:
if web page makes error 500, check it out if is native php error (only text) or nette error (with css style)
if it is native php error -> some problem with composer, check vendor directory, if is empty -> try to run composer install again

Turn on debug mode (bookmark to browser):
```
javascript:(/** @version 0.5.2 */function() {document.cookie='DEBUG_MODE='+'1'+';path=/;';})()
```
Turn off debug modu:

```
javascript:(/** @version 0.5.2 */function() {document.cookie='DEBUG_MODE='+'0'+';path=/;';})()
```


####Install administration

```
$ cd src/admin/new
$ npm i
$ npm run build
```

####Install frontend

Open your favorite Terminal and run this command.

```
$ npm run start
```

or

```
$ sh start.sh
```

## How To Use

Command `$ npm run dev` start virtual server at url [http://superadmin2019.yourname.l/](http://superadmin2019.yourname.l/) with browserSync and file watcher. Then any change in /src/ folder compile templates via. file watcher task and automaticly refresh templates in browser via browserSync task.

### Other Gulp Commands

Explain how to run the automated tests for this system

```
# command for build compressed templates
$ npm run build

# command for build compressed templates with file watcher and local server
$ npm run minwatch

# command for build compressed templetes with compress to zip file
$ npm run export

# command for W3C validate templates
$ npm run validate
```

### Directory Structure
```
.
├── node_modules                # NPM packages
├── app                         # Latte modules
│   ├── FrontModule             # Frontend templates and presenters
│   │   ├── components          # Templates with PHP presenters
│   │   ├── presenters          # PHP Presenters
│   │   ├── templates           # Latte templates
├── src                         # Source files
│   ├── css                     # Styles (using SASS - CSS extension language)
│   ├── img                     # Images
│   ├── js                      # Javascript
│   └── tpl                     # Templates (using Twig - templating language)
├── tasks                       # Set of asynchronous JavaScript functions
├── www                         # Generated files (html, css, js, images, fonts...)
├── .editorconfig               # Set of rules to maintain consistent coding convention between different editors and IDEs.
├── .eslintignore               # Set of ignored rules for coding convention
├── .eslintrc                   # Set of rules to highlight inappropriate coding convention in Javascript
├── .gitignore                  # List of excluded folders/files from git repository
├── .npmrc                      # The NPM config files
├── .stylintrc                  # Set of rules to hightlight inappropriate coding convention in styles
├── composer.json               # Backend dependencies
├── config.js                   # General devstack conig
├── gulpfile.js                 # Gulp config
└── package.json                # List of NPM dependencies
```

## Deployment

show new files to deploy
```
git ftp push -s stage -D
```

```
git ftp push -s stage
```

## Author

* **SUPERKODERS** - [www.superkoders.com](https://superkoders.com/) – [<EMAIL>](<EMAIL>)



