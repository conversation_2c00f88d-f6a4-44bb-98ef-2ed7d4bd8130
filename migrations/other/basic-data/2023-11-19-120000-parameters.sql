REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Total width', 'width', 'number', 0, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Total depth', 'depth', 'number', 1, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Total height', 'height', 'number', 2, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Seat height', 'vyska_sed', 'number', 2, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Width of seat', 'sirka_sed', 'number', 3, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Depth of seat', 'hloub_sed', 'number', 4, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Weight of product', 'weight', 'number', 5, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Stackability', 'stack_count', 'number', 11, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Connection into rows', 'line_connection', 'bool', 12, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Tag', 'tag', 'multiselect', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Position', 'position', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Rok realizace', 'yearRealization', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Typ realizace', 'typeRealization', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Lokace', 'location', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Product family', 'family', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Typ pobočky', 'showroomType', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Číslo výrobku', 'itemNumber', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('ID výrobku', 'itemId', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Jméno designéra', 'designerName', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Jméno modelu', 'modelName', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Skupina', 'group', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Délka', 'length', 'number', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Tlouštka plátu', 'thickness_of_the_plate', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Výška - lub', 'partial_height', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Vnitřní rozteč nohou - kratší', 'internal_leg_spacing_shorter', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Vnitřní rozteč nohou - delší', 'internal_leg_spacing_longer', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Vnitřní rozteč nohou - porozl', 'internal_leg_spacing_porozl', 'text', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Štos', 'stack', 'bool', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Řada - hladká', 'line_smooth', 'bool', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Řada - čalouh', 'line_upholstery', 'bool', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Řada - rákos', 'line_reed', 'bool', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Jméno fotografa', 'photographerName', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Jméno architekta', 'architectName', 'select', 50, 0, 1, '{}', 0, NULL);
REPLACE INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`) VALUES ('Typ produktu', 'productType', 'select', 50, 0, 1, '{}', 0, NULL);
