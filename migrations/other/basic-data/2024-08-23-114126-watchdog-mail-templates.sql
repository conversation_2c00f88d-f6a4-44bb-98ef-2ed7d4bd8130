-- new template - watchdog request
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (1, 'watchdogreq', 0, 0, 'WatchDogReq', 'Přihl<PERSON>šení hlídacího psa', '<p class="p1"><strong>Přihlášení hlídacího psa</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Země: </strong>[DATA-countryName]</p><p class="p2"><strong>Město: </strong>[DATA-city]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Kusy: </strong>[DATA-quantity]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (2, 'watchdogreq', 0, 0, 'WatchDogReq', 'Přihlášení hlídacího psa', '<p class="p1"><strong>Přihlášení hlídacího psa</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Země: </strong>[DATA-countryName]</p><p class="p2"><strong>Město: </strong>[DATA-city]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Kusy: </strong>[DATA-quantity]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (3, 'watchdogreq', 0, 0, 'WatchDogReq', 'Přihlášení hlídacího psa', '<p class="p1"><strong>Přihlášení hlídacího psa</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Země: </strong>[DATA-countryName]</p><p class="p2"><strong>Město: </strong>[DATA-city]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Kusy: </strong>[DATA-quantity]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (4, 'watchdogreq', 0, 0, 'WatchDogReq', 'Přihlášení hlídacího psa', '<p class="p1"><strong>Přihlášení hlídacího psa</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Země: </strong>[DATA-countryName]</p><p class="p2"><strong>Město: </strong>[DATA-city]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Kusy: </strong>[DATA-quantity]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (5, 'watchdogreq', 0, 0, 'WatchDogReq', 'Přihlášení hlídacího psa', '<p class="p1"><strong>Přihlášení hlídacího psa</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Země: </strong>[DATA-countryName]</p><p class="p2"><strong>Město: </strong>[DATA-city]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Kusy: </strong>[DATA-quantity]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (6, 'watchdogreq', 0, 0, 'WatchDogReq', 'Přihlášení hlídacího psa', '<p class="p1"><strong>Přihlášení hlídacího psa</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Země: </strong>[DATA-countryName]</p><p class="p2"><strong>Město: </strong>[DATA-city]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Kusy: </strong>[DATA-quantity]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (7, 'watchdogreq', 0, 0, 'WatchDogReq', 'Přihlášení hlídacího psa', '<p class="p1"><strong>Přihlášení hlídacího psa</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Země: </strong>[DATA-countryName]</p><p class="p2"><strong>Město: </strong>[DATA-city]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Kusy: </strong>[DATA-quantity]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');

-- update template watchdog alert
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 224;
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 225;
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 226;
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 227;
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 228;
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 229;
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 230;
UPDATE email_template t SET t.subject = 'Produkt opět skladem', t.body = '<p class="p1">Produkt<strong>[DATA-productName]</strong> je opět skladem!</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>' WHERE t.id = 277;
