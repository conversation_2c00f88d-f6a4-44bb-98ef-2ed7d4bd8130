-- new template - watchdog request
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (1, 'watchdog_company', 0, 0, 'WatchDogCompany', 'Produkt opět skladem', '<p class="p1"><strong>Rozesláno upozornění o novém naskladnění pro tyto produkty</strong></p><div>[CUSTOMERS_WATCHDOG_LIST]</div>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (2, 'watchdog_company', 0, 0, 'WatchDogCompany', 'Produkt opět skladem', '<p class="p1"><strong>Rozesláno upozornění o novém naskladnění pro tyto produkty</strong></p><div>[CUSTOMERS_WATCHDOG_LIST]</div>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (3, 'watchdog_company', 0, 0, 'WatchDogCompany', 'Produkt opět skladem', '<p class="p1"><strong>Rozesláno upozornění o novém naskladnění pro tyto produkty</strong></p><div>[CUSTOMERS_WATCHDOG_LIST]</div>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (4, 'watchdog_company', 0, 0, 'WatchDogCompany', 'Produkt opět skladem', '<p class="p1"><strong>Rozesláno upozornění o novém naskladnění pro tyto produkty</strong></p><div>[CUSTOMERS_WATCHDOG_LIST]</div>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (5, 'watchdog_company', 0, 0, 'WatchDogCompany', 'Produkt opět skladem', '<p class="p1"><strong>Rozesláno upozornění o novém naskladnění pro tyto produkty</strong></p><div>[CUSTOMERS_WATCHDOG_LIST]</div>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (6, 'watchdog_company', 0, 0, 'WatchDogCompany', 'Produkt opět skladem', '<p class="p1"><strong>Rozesláno upozornění o novém naskladnění pro tyto produkty</strong></p><div>[CUSTOMERS_WATCHDOG_LIST]</div>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (7, 'watchdog_company', 0, 0, 'WatchDogCompany', 'Produkt opět skladem', '<p class="p1"><strong>Rozesláno upozornění o novém naskladnění pro tyto produkty</strong></p><div>[CUSTOMERS_WATCHDOG_LIST]</div>');
