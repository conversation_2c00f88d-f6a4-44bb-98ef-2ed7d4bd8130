CREATE TABLE `configurator_parameter_extender` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`code` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`editedAt` DATETIME NOT NULL,
	`createdAt` DATETIME NOT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `code` (`code`) USING BTREE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1;



ALTER TABLE `configurator_parameter`
	ADD COLUMN `extenderId` INT(11) NOT NULL AFTER `id`;


ALTER TABLE `configurator_parameter`
	ADD CONSTRAINT `FK_configurator_parameter_configurator_parameter_extender` FOREI<PERSON>N KEY (`extenderId`) REFERENCES `configurator_parameter_extender` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
