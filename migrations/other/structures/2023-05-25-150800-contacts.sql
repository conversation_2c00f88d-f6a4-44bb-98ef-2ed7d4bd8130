DROP TABLE IF EXISTS `contact`;
CREATE TABLE `contact` (
						 `id` int(11) NOT NULL AUTO_INCREMENT,
						 `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
						 `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
						 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `contact_localization`;
CREATE TABLE `contact_localization` (
									  `id` int(11) NOT NULL AUTO_INCREMENT,
									  `mutationId` int(11) NOT NULL,
									  `contactId` int(11) NOT NULL,
									  `name` varchar(250) NOT NULL,
									  `nameTitle` varchar(250) NOT NULL,
									  `nameAnchor` varchar(250) NOT NULL,
									  `description` text NOT NULL,
									  `keywords` text NOT NULL,
									  `public` int(11) NOT NULL,
									  `publicFrom` datetime DEFAULT NULL,
									  `publicTo` datetime DEFAULT NULL,
									  `edited` int(11) DEFAULT NULL,
									  `editedTime` datetime DEFAULT NULL,
									  `customFieldsJson` longtext,
									  `customContentSchemeJson` longtext,
									  `customContentJson` longtext,
									  `downloadsNumber` int(10) NOT NULL,
									  PRIMARY KEY (`id`),
									  KEY `mutationId` (`mutationId`),
									  KEY `contactId` (`contactId`),
									  CONSTRAINT `contact_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
									  CONSTRAINT `contact_localization_ibfk_2` FOREIGN KEY (`contactId`) REFERENCES `contact` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


