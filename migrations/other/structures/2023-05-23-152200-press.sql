CREATE TABLE `press` (
	 `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
	 `internalName` varchar(250) NOT NULL
);

CREATE TABLE `press_localization` (
	  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
	  `mutationId` int(11) NOT NULL,
	  `pressId` int(11) NOT NULL,
	  `name` varchar(250) NOT NULL,
	  `public` int(11) NOT NULL,
	  `publicFrom` datetime NULL,
	  `publicTo` datetime NULL,
	  `edited` int(11) NULL,
	  `editedTime` datetime NULL,
	  `customFields<PERSON>son` longtext NULL,
	  `downloadsNumber` int(10) NOT NULL,
	  FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
	  FOREIGN KEY (`pressId`) REFERENCES `press` (`id`)
);


ALTER TABLE `press`
	CHANGE `internalName` `internalName` varchar(255) COLLATE 'utf8mb4_unicode_520_ci' NOT NULL AFTER `id`,
	ADD `customFieldsJson` longtext COLLATE 'utf8mb4_unicode_520_ci' NULL;
