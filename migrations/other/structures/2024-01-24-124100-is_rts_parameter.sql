INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Dostupnost', 'availability', 'select', 61, 0, 1, '{}', 0, NULL);

set @parameterId = LAST_INSERT_ID();

INSERT INTO `parameter_value` (`parameterId`, `internalValue`, `internalAlias`, `sort`, `parameterSort`, `extId`, `customFieldsJson`)
VALUES (@parameterId, 'Skladem', 'in_stock', 0, 0, NULL, '{}');

INSERT INTO `parameter_value` (`parameterId`, `internalValue`, `internalAlias`, `sort`, `parameterSort`, `extId`, `customFieldsJson`)
VALUES (@parameterId, 'Na dotaz', 'on_request', 0, 0, NULL, '{}');

