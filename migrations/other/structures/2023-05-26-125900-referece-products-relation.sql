CREATE TABLE `reference_x_product` (
   `referenceId` INT(11) NOT NULL,
   `productId` INT(11) NOT NULL,
   PRIMARY KEY (`referenceId`, `productId`) USING BTREE,
   INDEX `FK_reference_x_product_reference` (`referenceId`) USING BTREE,
   INDEX `FK_reference_x_product_product` (`productId`) USING BTREE,
   CONSTRAINT `FK_reference_x_product_reference` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_reference_x_product_product` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
;
