ALTER TABLE `configurator_parameter`
	ADD COLUMN `pl` VARCHAR(255) NOT NULL AFTER `cs`;

ALTER TABLE `configurator_parameter`
	ADD COLUMN `de` VARCHAR(255) NOT NULL AFTER `pl`;

ALTER TABLE `configurator_parameter`
	ADD COLUMN `fr` VARCHAR(255) NOT NULL AFTER `pl`;

ALTER TABLE `configurator_parameter`
	ADD COLUMN `it` VARCHAR(255) NOT NULL AFTER `pl`;

ALTER TABLE `state`
	ADD COLUMN `pl` VARCHAR(100) NOT NULL DEFAULT '' AFTER `name`;

ALTER TABLE `state`
	ADD COLUMN `de` VARCHAR(100) NOT NULL DEFAULT '' AFTER `pl`;

ALTER TABLE `state`
	ADD COLUMN `fr` VARCHAR(100) NOT NULL DEFAULT '' AFTER `pl`;
ALTER TABLE `state`
	ADD COLUMN `it` VARCHAR(100) NOT NULL DEFAULT '' AFTER `pl`;


