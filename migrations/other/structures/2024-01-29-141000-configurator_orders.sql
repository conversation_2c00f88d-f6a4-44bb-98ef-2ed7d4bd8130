CREATE TABLE `configuration_order` (
   `id` INT(11) NOT NULL AUTO_INCREMENT,
   `code` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `mutationId` INT(11) NOT NULL,
   `userId` INT(11) NULL DEFAULT NULL,
   `variantId` INT(11) NOT NULL,
   `stateId` INT(11) NOT NULL,
   `name` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `phone` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `email` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `hash` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `createdAt` DATETIME NOT NULL,
   `noticeSentAt` DATETIME NULL DEFAULT NULL,
   `inquirySentAt` DATETIME NULL DEFAULT NULL,
   `offerSentAt` DATETIME NULL DEFAULT NULL,
   PRIMARY KEY (`id`) USING BTREE,
   INDEX `code` (`code`) USING BTREE,
   INDEX `FK_configurator_order_mutation` (`mutationId`) USING BTREE,
   INDEX `FK_configurator_order_user` (`userId`) USING BTREE,
   INDEX `FK_configurator_order_product_variant` (`variantId`) USING BTREE,
   INDEX `FK_configuration_order_state` (`stateId`) USING BTREE,
   CONSTRAINT `FK_configuration_order_state` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT,
   CONSTRAINT `FK_configurator_order_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_configurator_order_product_variant` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT,
   CONSTRAINT `FK_configurator_order_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON UPDATE CASCADE ON DELETE SET NULL
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1
;


DELETE FROM `email_template` WHERE  `id`=1;
DELETE FROM `email_template` WHERE  `id`=63;
DELETE FROM `email_template` WHERE  `id`=2;
DELETE FROM `email_template` WHERE  `id`=64;
DELETE FROM `email_template` WHERE  `id`=3;
DELETE FROM `email_template` WHERE  `id`=65;
DELETE FROM `email_template` WHERE  `id`=4;
DELETE FROM `email_template` WHERE  `id`=66;
DELETE FROM `email_template` WHERE  `id`=62;
DELETE FROM `email_template` WHERE  `id`=80;

DELETE FROM `email_template` WHERE  `id`=55;
DELETE FROM `email_template` WHERE  `id`=74;
DELETE FROM `email_template` WHERE  `id`=21;
DELETE FROM `email_template` WHERE  `id`=71;
DELETE FROM `email_template` WHERE  `id`=58;
DELETE FROM `email_template` WHERE  `id`=77;
DELETE FROM `email_template` WHERE  `id`=13;
DELETE FROM `email_template` WHERE  `id`=69;
DELETE FROM `email_template` WHERE  `id`=50;
DELETE FROM `email_template` WHERE  `id`=73;
DELETE FROM `email_template` WHERE  `id`=57;
DELETE FROM `email_template` WHERE  `id`=76;


INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`)
VALUES (2, 'orderInquiry', 0, 0, 'Kontrola poptávky', 'Kontrola poptávky', '<h2><strong>Kontrola poptávky</strong></h2>\n\n<p>Vypadá to že poptávka [DATA-code] nebyla ještě zodpovězena.</p>\n\n\n<p><strong>Jméno: </strong>[DATA-name]</p>\n\n<p><strong>E-mail: </strong>[DATA-email]</p>\n\n<p><strong>Telefón: </strong>[DATA-phone]</p>\n\n<p><strong>Kód poptávky: </strong>[DATA-code]</p>\n\n<p><strong>Odkaz pro potvrzení odeslání nabídky: </strong><a href="[DATA-link]">[DATA-link]</a></p>\n');

INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`)
VALUES (2, 'orderNotify', 0, 0, 'Nová poptávka', 'Nová poptávka', '<h2><strong>Nová poptávka</strong></h2>\n\n<p><strong>Jméno: </strong>[DATA-name]</p>\n\n<p><strong>E-mail: </strong>[DATA-email]</p>\n\n<p><strong>Telefón: </strong>[DATA-phone]</p>\n\n<p><strong>Kód poptávky: </strong>[DATA-code]</p>\n\n<p><strong>Odkaz pro potvrzení odeslání nabídky: </strong><a href="[DATA-link]">[DATA-link]</a></p>\n');
