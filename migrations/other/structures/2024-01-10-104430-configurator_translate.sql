CREATE TABLE `configurator_parameter` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `namespace` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `type` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `uid` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `parameterName` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `code` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `parameterCode` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `name` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `cs` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `en` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
  `editedAt` DATETIME NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `namespace_uid` (`namespace`, `uid`) USING BTREE,
  INDEX `uid` (`uid`) USING BTREE,
  INDEX `type` (`type`) USING BTREE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;
