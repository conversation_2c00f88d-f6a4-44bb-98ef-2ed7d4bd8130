CREATE TABLE `import_cache` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`type` VARCHAR(256) NOT NULL COLLATE 'utf8mb4_czech_ci',
	`data` MEDIUMTEXT NOT NULL COLLATE 'utf8mb4_czech_ci',
	`status` ENUM('new','ready','imported','error','processing','skipped') NOT NULL DEFAULT 'new' COLLATE 'utf8mb4_czech_ci',
	`extId` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	`createdTime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`importedTime` DATETIME NULL DEFAULT NULL,
	`message` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_czech_ci',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `type_status_createdTime` (`type`, `status`, `createdTime`) USING BTREE,
	INDEX `createdTime` (`createdTime`) USING BTREE,
	INDEX `status` (`status`) USING BTREE,
	INDEX `type_status_extId` (`type`, `status`, `extId`) USING BTREE
)
	COLLATE='utf8mb4_czech_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1546
;


CREATE TABLE `mutation_setting` (
									`id` int(11) NOT NULL AUTO_INCREMENT,
									`mutation` int(11) NOT NULL,
									`key` varchar(128) COLLATE utf8mb4_unicode_520_ci NOT NULL,
									`type` varchar(64) COLLATE utf8mb4_unicode_520_ci NOT NULL,
									`value` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
									PRIMARY KEY (`id`),
									KEY `mutation` (`mutation`),
									CONSTRAINT `mutation_setting_ibfk_2` FOREIGN KEY (`mutation`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;



ALTER TABLE `product`
	CHANGE COLUMN `uid` `uid` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci' AFTER `id`,
	ADD UNIQUE INDEX `uid` (`uid`);



