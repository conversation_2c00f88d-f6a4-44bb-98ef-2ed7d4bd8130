DROP TABLE IF EXISTS `service`;
CREATE TABLE `service` (
						 `id` int(11) NOT NULL AUTO_INCREMENT,
						 `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
						 `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
						 `customContentJson` longtext,
						 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `service_localization`;
CREATE TABLE `service_localization` (
									  `id` int(11) NOT NULL AUTO_INCREMENT,
									  `mutationId` int(11) NOT NULL,
									  `serviceId` int(11) NOT NULL,
									  `name` varchar(250) NOT NULL,
									  `nameTitle` varchar(250) NOT NULL,
									  `nameAnchor` varchar(250) NOT NULL,
									  `description` text NOT NULL,
									  `keywords` text NOT NULL,
									  `public` int(11) NOT NULL,
									  `publicFrom` datetime DEFAULT NULL,
									  `publicTo` datetime DEFAULT NULL,
									  `edited` int(11) DEFAULT NULL,
									  `editedTime` datetime DEFAULT NULL,
									  `customFieldsJson` longtext,
									  `customContentJson` longtext,
									  PRIMARY KEY (`id`),
									  KEY `mutationId` (`mutationId`),
									  KEY `serviceId` (`serviceId`),
									  CONSTRAINT `service_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
									  CONSTRAINT `service_localization_ibfk_2` FOREIGN KEY (`serviceId`) REFERENCES `service` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


