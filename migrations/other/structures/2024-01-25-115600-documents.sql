-- --------------------------------------------------------
-- Hostitel:                     localhost
-- Verze serveru:                5.7.24 - MySQL Community Server (GPL)
-- OS serveru:                   Win64
-- HeidiSQL Verze:               12.6.0.6765
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Exportování struktury pro tabulka ton.document
DROP TABLE IF EXISTS `document`;
CREATE TABLE IF NOT EXISTS `document` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`documentGroup1Id` int(11) DEFAULT NULL,
	`documentGroup2Id` int(11) DEFAULT NULL,
	`extId` int(11) NOT NULL,
	`fileName` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`directory1` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`directory2` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`hiUrl` text COLLATE utf8mb4_unicode_520_ci,
	`lowUrl` text COLLATE utf8mb4_unicode_520_ci,
	`originalDocumentId` int(11) NOT NULL COMMENT 'IDDOK',
	`sort` int(11) NOT NULL DEFAULT '0',
	`updatedAt` datetime NOT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	KEY `FK_document_document_group` (`documentGroup1Id`) USING BTREE,
	KEY `FK_document_document_group_2` (`documentGroup2Id`),
	CONSTRAINT `FK_document_document_group` FOREIGN KEY (`documentGroup1Id`) REFERENCES `document_group` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
	CONSTRAINT `FK_document_document_group_2` FOREIGN KEY (`documentGroup2Id`) REFERENCES `document_group` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=158151 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- Export dat nebyl vybrán.

-- Exportování struktury pro tabulka ton.document_group
DROP TABLE IF EXISTS `document_group`;
CREATE TABLE IF NOT EXISTS `document_group` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`alias` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`updatedAt` datetime NOT NULL,
	`extId` int(11) NOT NULL,
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=345 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- Export dat nebyl vybrán.

-- Exportování struktury pro tabulka ton.document_product
DROP TABLE IF EXISTS `document_product`;
CREATE TABLE IF NOT EXISTS `document_product` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`productVariantId` int(11) DEFAULT NULL,
	`documentId` int(11) DEFAULT NULL,
	`documentGroupId` int(11) DEFAULT NULL,
	`name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`updatedAt` datetime NOT NULL,
	`extId` int(11) NOT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `extId` (`extId`),
	KEY `FK_document_product_product_variant` (`productVariantId`) USING BTREE,
	KEY `FK_document_product_document` (`documentId`) USING BTREE,
	KEY `FK_document_product_document_group` (`documentGroupId`) USING BTREE,
	CONSTRAINT `FK_document_product_document` FOREIGN KEY (`documentId`) REFERENCES `document` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_document_product_document_group` FOREIGN KEY (`documentGroupId`) REFERENCES `document_group` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_document_product_product_variant` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=158151 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci ROW_FORMAT=DYNAMIC;

-- Export dat nebyl vybrán.

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
