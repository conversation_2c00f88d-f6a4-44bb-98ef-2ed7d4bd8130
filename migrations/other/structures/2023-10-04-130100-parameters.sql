INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `customFieldsJson`) VALUES ('<PERSON><PERSON>lo výrobku', 'itemNumber', 'text', 0, 0, '{}');
INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `customFieldsJson`) VALUES ('ID výrobku', 'itemId', 'text', 0, 0, '{}');
INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `customFieldsJson`) VALUES ('Jméno designera', 'designerName', 'text', 0, 0, '{}');
INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `customFieldsJson`) VALUES ('Jméno modelu', 'modelName', 'text', 0, 0, '{}');
INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `customFieldsJson`) VALUES ('Skupina', 'group', 'text', 0, 0, '{}');


UPDATE `parameter` SET `type`='select' WHERE  `uid`='designerName';
