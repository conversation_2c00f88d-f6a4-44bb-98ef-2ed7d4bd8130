ALTER TABLE `document_product`
	DROP FOREIGN KEY `FK_document_product_document_group`;

ALTER TABLE `document_product`
DROP COLUMN `documentGroupId`,
	DROP COLUMN `name`,
	DROP COLUMN `updatedAt`,
	DROP COLUMN `extId`;


ALTER TABLE `document`
	ADD COLUMN `variantId` INT(11) NULL DEFAULT NULL AFTER `mutationId`,
	ADD CONSTRAINT `FK_document_product_variant` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `document`
DROP FOREIGN KEY `FK_document_product_variant`;
ALTER TABLE `document`
	CHANGE COLUMN `variantId` `productVariantId` INT(11) NULL DEFAULT NULL AFTER `mutationId`,
DROP INDEX `FK_document_product_variant`,
	ADD INDEX `FK_document_product_variant` (`productVariantId`) USING BTREE,
	ADD CONSTRAINT `FK_document_product_variant` FOREI<PERSON>N KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
