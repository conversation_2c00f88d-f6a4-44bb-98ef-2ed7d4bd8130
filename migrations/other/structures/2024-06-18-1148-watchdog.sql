
CREATE TABLE IF NOT EXISTS `watchdog` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) NOT NULL DEFAULT '1',
	`productInstanceId` int(11) NOT NULL,
	`quantity` int(11) NOT NULL,
	`name` varchar(250) COLLATE utf8mb4_czech_ci DEFAULT NULL,
	`email` varchar(250) COLLATE utf8mb4_czech_ci DEFAULT NULL,
	`reactivationHash` varchar(250) COLLATE utf8mb4_czech_ci DEFAULT NULL,
	`isActive` int(11) DEFAULT NULL,
	`isDelivered` int(11) DEFAULT NULL,
	`created` datetime NOT NULL,
	PRIMARY KEY (`id`),
	<PERSON>EY `mutationId` (`mutationId`),
	KEY `productInstanceId` (`productInstanceId`),
	CONSTRAINT `watchdog_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `watchdog_ibfk_4` FOREIGN KEY (`productInstanceId`) REFERENCES `product_instance` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;



INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (1, 'watchdog', 0, 0, 'WatchDog', 'WatchDog', '<p class="p1"><strong>Produkt skladem</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (2, 'watchdog', 0, 0, 'WatchDog', 'WatchDog', '<p class="p1"><strong>Produkt skladem</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (3, 'watchdog', 0, 0, 'WatchDog', 'WatchDog', '<p class="p1"><strong>Produkt skladem</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (4, 'watchdog', 0, 0, 'WatchDog', 'WatchDog', '<p class="p1"><strong>Produkt skladem</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (5, 'watchdog', 0, 0, 'WatchDog', 'WatchDog', '<p class="p1"><strong>Produkt skladem</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (6, 'watchdog', 0, 0, 'WatchDog', 'WatchDog', '<p class="p1"><strong>Produkt skladem</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
INSERT INTO `email_template` (`mutationId`, `key`, `isDeveloper`, `isHidden`, `name`, `subject`, `body`) VALUES (7, 'watchdog', 0, 0, 'WatchDog', 'WatchDog', '<p class="p1"><strong>Produkt skladem</strong></p><p class="p2"><strong>Email: </strong>[DATA-email]</p><p class="p2"><strong>Jméno: </strong>[DATA-name]</p><p class="p2"><strong>Jméno produktu: </strong>[DATA-productName]</p><p class="p2"><strong>Odkaz: </strong>[DATA-pageLink]</p>');
