CREATE TABLE `product_instance` (
									`id` INT(11) NOT NULL AUTO_INCREMENT,
									`product` INT(11) NULL DEFAULT NULL,
									`internalName` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
									`customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
									`customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
									`stock` INT(11) NOT NULL,
									PRIMARY KEY (`id`) USING BTREE,
									INDEX `FK_product_instance_product` (`product`) USING BTREE,
									CONSTRAINT `FK_product_instance_product` FOREIGN KEY (`product`) REFERENCES `product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=4
;


CREATE TABLE `product_instance_localization` (
	 `id` INT(11) NOT NULL AUTO_INCREMENT,
	 `mutationId` INT(11) NOT NULL,
	 `productInstanceId` INT(11) NOT NULL,
	 `name` VARCHAR(250) NOT NULL COLLATE 'utf8_general_ci',
	 `public` INT(11) NOT NULL,
	 `publicFrom` DATETIME NULL DEFAULT NULL,
	 `publicTo` DATETIME NULL DEFAULT NULL,
	 `edited` INT(11) NULL DEFAULT NULL,
	 `editedTime` DATETIME NULL DEFAULT NULL,
	 `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	 `customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',
	 PRIMARY KEY (`id`) USING BTREE,
	 INDEX `mutationId` (`mutationId`) USING BTREE,
	 INDEX `productInstanceId` (`productInstanceId`) USING BTREE,
	 CONSTRAINT `product_instance_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	 CONSTRAINT `product_instance_localization_ibfk_2` FOREIGN KEY (`productInstanceId`) REFERENCES `product_instance` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=3
;
