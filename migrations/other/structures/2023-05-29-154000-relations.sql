ALTER TABLE `press`
	ADD `contactId` int(11) NULL,
ADD FOREIGN KEY (`contactId`) REFERENCES `contact` (`id`);


CREATE TABLE `showroom_x_contact` (
									   `showroomId` INT(11) NOT NULL,
									   `contactId` INT(11) NOT NULL,
									   PRIMARY KEY (`showroomId`, `contactId`) USING BTREE,
									   INDEX `FK_showroom_x_contact_showroom` (`showroomId`) USING BTREE,
									   INDEX `FK_showroom_x_contact_contact` (`contactId`) USING BTREE,
									   CONSTRAINT `FK_showroom_x_contact_showroom` FOREIGN KEY (`showroomId`) REFERENCES `showroom` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
									   CONSTRAINT `FK_showroom_x_contact_contact` FOREIGN KEY (`contactId`) REFERENCES `contact` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
;

ALTER TABLE `showroom`
	ADD `contactId` int(11) NULL,
ADD FOREIGN KEY (`contactId`) REFERENCES `contact` (`id`);
