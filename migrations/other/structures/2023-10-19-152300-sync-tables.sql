CREATE TABLE `document` (
							`id` INT(11) NOT NULL AUTO_INCREMENT,
							`documentGroupId` INT(11) NULL DEFAULT NULL,
							`extId` INT(11) NOT NULL,
							`fileName` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
							`directory1` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
							`directory2` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
							`sort` INT(11) NOT NULL DEFAULT '0',
							PRIMARY KEY (`id`) USING BTREE,
							INDEX `FK_document_document_group` (`documentGroupId`) USING BTREE,
							CONSTRAINT `FK_document_document_group` FOREIGN KEY (`documentGroupId`) REFERENCES `document_group` (`id`) ON UPDATE CASCADE ON DELETE SET NULL
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1;

CREATE TABLE `document_group` (
								  `id` INT(11) NOT NULL AUTO_INCREMENT,
								  `name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
								  `extId` INT(11) NOT NULL,
								  PRIMARY KEY (`id`) USING BTREE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1;

CREATE TABLE `document_product` (
									`id` INT(11) NOT NULL AUTO_INCREMENT,
									`productVariantId` INT(11) NULL DEFAULT NULL,
									`documentId` INT(11) NULL DEFAULT NULL,
									`documentGroupId` INT(11) NULL DEFAULT NULL,
									`extId` INT(11) NOT NULL,
									`name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
									PRIMARY KEY (`id`) USING BTREE,
									INDEX `FK_document_product_product_variant` (`productVariantId`) USING BTREE,
									INDEX `FK_document_product_document` (`documentId`) USING BTREE,
									INDEX `FK_document_product_document_group` (`documentGroupId`) USING BTREE,
									CONSTRAINT `FK_document_product_document` FOREIGN KEY (`documentId`) REFERENCES `document` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
									CONSTRAINT `FK_document_product_document_group` FOREIGN KEY (`documentGroupId`) REFERENCES `document_group` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
									CONSTRAINT `FK_document_product_product_variant` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
ROW_FORMAT=DYNAMIC
AUTO_INCREMENT=1;


CREATE TABLE `product_group` (
								 `id` INT(11) NOT NULL AUTO_INCREMENT,
								 `name` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
								 `extId` INT(11) NOT NULL,
								 `productVariantId` INT(11) NOT NULL,
								 PRIMARY KEY (`id`) USING BTREE,
								 INDEX `FK_product_group_product_variant` (`productVariantId`) USING BTREE,
								 CONSTRAINT `FK_product_group_product_variant` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
ROW_FORMAT=DYNAMIC
AUTO_INCREMENT=1;



