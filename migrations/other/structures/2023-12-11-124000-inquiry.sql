CREATE TABLE `inquiry` (
						   `id` INT(11) NOT NULL AUTO_INCREMENT,
						   `name` VA<PERSON><PERSON><PERSON>(255) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
						   `email` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
						   `phone` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
						   `count` INT(11) NULL DEFAULT NULL,
						   `zipCode` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
						   `note` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
						   `createdAt` DATETIME NOT NULL,
						   `stateId` INT(11) NULL DEFAULT NULL,
						   `inquiryDetail` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
						   PRIMARY KEY (`id`) USING BTREE,
						   INDEX `FK_inquiry_state` (`stateId`) USING BTREE,
						   CONSTRAINT `FK_inquiry_state` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
AUTO_INCREMENT=14
;
