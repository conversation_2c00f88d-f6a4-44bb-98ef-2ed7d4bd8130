CREATE TABLE `reference_x_reference` (
   `referenceId` INT(11) NOT NULL,
   `attachedReferenceId` INT(11) NOT NULL,
   PRIMARY KEY (`referenceId`, `attachedReferenceId`) USING BTREE,
   INDEX `FK_reference_x_reference_reference` (`referenceId`) USING BTREE,
   INDEX `FK_reference_x_reference_reference_2` (`attachedReferenceId`) USING BTREE,
   CONSTRAINT `FK_reference_x_reference_reference` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_reference_x_reference_reference_2` FOREIGN KEY (`attachedReferenceId`) REFERENCES `reference` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
;
