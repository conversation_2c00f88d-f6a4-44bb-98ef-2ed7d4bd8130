CREATE TABLE `admin` (
 `id` INT(11) NOT NULL AUTO_INCREMENT,
 `firstname` VA<PERSON><PERSON><PERSON>(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
 `lastname` VA<PERSON>HAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
 `email` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
 `password` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
 `role` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE INDEX `email` (`email`) USING BTREE
)
	COLLATE='utf8mb4_unicode_520_ci'
;



INSERT INTO `admin`
SELECT (u.id + 10000) as id, u.firstname as firstname, u.lastname as lastname, u.email as email, u.password as password, u.role as role FROM user AS u WHERE u.role IN ('admin', 'developer');

UPDATE `user` SET `role`='user';

ALTER TABLE `user_hash`
	CHANGE COLUMN `userId` `userId` INT(11) NULL AFTER `id`,
	ADD COLUMN `adminId` INT(11) NULL AFTER `userId`;


ALTER TABLE `user_hash`
DROP FOREIGN KEY `user_hash_ibfk_1`;
ALTER TABLE `user_hash`
	ADD CONSTRAINT `user_hash_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	ADD CONSTRAINT `FK_user_hash_admin` FOREIGN KEY (`adminId`) REFERENCES `admin` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

DELETE FROM `admin` WHERE  `id`=10010;
DELETE FROM `admin` WHERE  `id`=10023;
DELETE FROM `admin` WHERE  `id`=10024;
DELETE FROM `admin` WHERE  `id`=10025;
DELETE FROM `admin` WHERE  `id`=10027;
DELETE FROM `admin` WHERE  `id`=10029;
