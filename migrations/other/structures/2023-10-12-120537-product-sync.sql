ALTER TABLE `product_parameter`
DROP FOREIGN KEY `product_parameter_ibfk_5`,
	DROP FOREIGN KEY `product_parameter_ibfk_6`;
ALTER TABLE `product_parameter`
	ADD CONSTRAINT `product_parameter_ibfk_5` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	ADD CONSTRAINT `product_parameter_ibfk_6` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;



INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Šířka', 'width', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('<PERSON><PERSON>š<PERSON>', 'height', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Délka', 'length', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Hloubka', 'depth', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Váha', 'weight', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Výška sedáku', 'vyska_sed', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Hloubka sedáku', 'hloub_sed', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Šířka sedáku', 'sirka_sed', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Tlouštka plátu', 'thickness_of_the_plate', 'text', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Výška - lub', 'partial_height', 'text', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Vnitřní rozteč nohou - kratší', 'internal_leg_spacing_shorter', 'text', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Vnitřní rozteč nohou - delší', 'internal_leg_spacing_longer', 'text', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Vnitřní rozteč nohou - porozl', 'internal_leg_spacing_porozl', 'text', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Štos - počet ', 'stack_count', 'number', 0, 0, 0, '{}', 0, NULL);

INSERT INTO `parameter` (`name`, `uid`, `type`, `sort`, `variantParameter`, `isInFilter`, `customFieldsJson`, `isProtected`, `extId`)
VALUES ('Štos', 'stack', 'bool', 0, 0, 0, '{}', 0, NULL);

