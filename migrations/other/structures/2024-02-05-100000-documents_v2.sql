
CREATE TABLE `document_group_product` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VA<PERSON>HA<PERSON>(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
  `alias` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
  `updatedAt` DATETIME NOT NULL,
  `extId` INT(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1
;


ALTER TABLE `document_group_product`
	ADD COLUMN `directory` VARCHAR(255) NOT NULL DEFAULT '' AFTER `alias`;

ALTER TABLE `document_group`
	ADD COLUMN `directory` VARCHAR(255) NOT NULL DEFAULT '' AFTER `alias`;

ALTER TABLE `document`
DROP FOREIGN KEY `FK_document_document_group_2`,
	DROP FOREIGN KEY `FK_document_document_group`;



ALTER TABLE `document`
	<PERSON>ANGE COLUMN `documentGroup1Id` `documentGroupId` INT(11) NULL DEFAULT NULL AFTER `id`,
	CHANGE COLUMN `documentGroup2Id` `documentGroupProductId` INT(11) NULL DEFAULT NULL AFTER `documentGroupId`,
DROP INDEX `FK_document_document_group`,
	ADD INDEX `FK_document_document_group` (`documentGroupId`) USING BTREE,
DROP INDEX `FK_document_document_group_2`,
	ADD INDEX `FK_document_document_group_2` (`documentGroupProductId`) USING BTREE;


ALTER TABLE `document`
	ADD CONSTRAINT `FK_document_document_group` FOREIGN KEY (`documentGroupId`) REFERENCES `document_group` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
	ADD CONSTRAINT `FK_document_document_group_product` FOREIGN KEY (`documentGroupProductId`) REFERENCES `document_group_product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `document`
	ADD COLUMN `mutationId` INT(11) NULL DEFAULT NULL AFTER `documentGroupProductId`;

ALTER TABLE `document`
	ADD CONSTRAINT `FK_document_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `mutation`
	ADD COLUMN `pimpCode` VARCHAR(250) NOT NULL DEFAULT '' AFTER `fromEmailName`;

UPDATE `mutation` SET `pimpCode`='eng' WHERE  `id`=2;
