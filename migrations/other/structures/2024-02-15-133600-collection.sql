CREATE TABLE `collection` (
	  `id` INT(11) NOT NULL AUTO_INCREMENT,
	  `internalName` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
	  `customFields<PERSON>son` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
	  PRIMARY KEY (`id`) USING BTREE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
AUTO_INCREMENT=5
;


CREATE TABLE `collection_localization` (
   `id` INT(11) NOT NULL AUTO_INCREMENT,
   `mutationId` INT(11) NOT NULL,
   `collectionId` INT(11) NOT NULL,
   `name` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
   `nameAnchor` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
   `nameTitle` VARCHAR(250) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
   `description` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `keywords` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `public` INT(11) NOT NULL DEFAULT '0',
   `forceNoIndex` INT(11) NOT NULL DEFAULT '0',
   `hideInSearch` INT(11) NOT NULL DEFAULT '0',
   `hideInSitemap` INT(11) NOT NULL DEFAULT '0',
   `publicFrom` DATETIME NULL DEFAULT NULL,
   `publicTo` DATETIME NULL DEFAULT NULL,
   `edited` INT(11) NULL DEFAULT NULL,
   `editedTime` DATETIME NULL DEFAULT NULL,
   `customFieldsJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
   `customContentJson` LONGTEXT NULL DEFAULT NULL COLLATE 'utf8mb4_unicode_520_ci',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE INDEX `mutationId_collectionId` (`mutationId`, `collectionId`) USING BTREE,
   INDEX `FK_collection_mutation` (`mutationId`) USING BTREE,
   INDEX `FK_collection_localization_collection` (`collectionId`) USING BTREE,
   CONSTRAINT `FK_collection_localization_collection` FOREIGN KEY (`collectionId`) REFERENCES `collection` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
   CONSTRAINT `FK_collection_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8_bin'
ENGINE=InnoDB
AUTO_INCREMENT=500
;


ALTER TABLE `product`
	ADD COLUMN `collectionId` INT(11) NULL AFTER `id`;

ALTER TABLE `product`
	ADD CONSTRAINT `FK_product_collection` FOREIGN KEY (`collectionId`) REFERENCES `collection` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;
