








ALTER TABLE `product_instance`
DROP COLUMN `product`,
	DROP FOREIGN KEY `FK_product_instance_product`;


ALTER TABLE `product_instance`
	ADD COLUMN `variantId` INT NOT NULL AFTER `id`;
ALTER TABLE `product_instance`
	ADD CONSTRAINT `FK_product_instance_product_variant` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;




ALTER TABLE `product_instance`
	ADD COLUMN `colorOption` INT(11) NOT NULL DEFAULT '0' AFTER `variantId`,
	ADD COLUMN `coverOption` INT(11) NOT NULL DEFAULT '0' AFTER `colorOption`;
