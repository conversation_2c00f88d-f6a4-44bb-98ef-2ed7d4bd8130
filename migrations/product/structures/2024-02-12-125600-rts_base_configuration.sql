ALTER TABLE `product_instance`
	ADD COLUMN `baseConfiguration` VARCHAR(255) NOT NULL AFTER `internalName`;

ALTER TABLE `configuration_order`
	ADD COLUMN `productInstanceId` INT(11) NULL AFTER `variantId`;

ALTER TABLE `configuration_order`
	ADD CONSTRAINT `FK_configuration_order_product_instance` FOREIGN KEY (`productInstanceId`) REFERENCES `product_instance` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
