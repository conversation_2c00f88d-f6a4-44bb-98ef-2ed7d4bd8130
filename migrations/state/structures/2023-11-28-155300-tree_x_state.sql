CREATE TABLE `tree_state` (
							  `id` INT(11) NOT NULL AUTO_INCREMENT,
							  `treeId` INT(11) NOT NULL,
							  `stateId` INT(11) NOT NULL,
							  PRIMARY KEY (`id`) USING BTREE,
							  INDEX `FK__tree` (`treeId`) USING BTREE,
							  INDEX `FK__state` (`stateId`) USING BTREE,
							  CONSTRAINT `FK__state` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
							  CONSTRAINT `FK__tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;
