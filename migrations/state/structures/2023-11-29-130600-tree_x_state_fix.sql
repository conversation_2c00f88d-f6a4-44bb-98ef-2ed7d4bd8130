DROP TABLE `tree_state`;


CREATE TABLE `service_x_state` (
								 `id` INT(11) NOT NULL AUTO_INCREMENT,
								 `serviceId` INT(11) NOT NULL,
								 `stateId` INT(11) NOT NULL,
								 PRIMARY KEY (`id`) USING BTREE,
								 INDEX `FK__service` (`serviceId`) USING BTREE,
								 INDEX `FK__state` (`stateId`) USING BTREE,
								 CONSTRAINT `FK__service` FOREIGN KEY (`serviceId`) REFERENCES `service` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
								 CONSTRAINT `FK__state` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;

CREATE TABLE `contact_x_state` (
								 `id` INT(11) NOT NULL AUTO_INCREMENT,
								 `contactId` INT(11) NOT NULL,
								 `stateId` INT(11) NOT NULL,
								 PRIMARY KEY (`id`) USING BTREE,
								 INDEX `FK_contact_state_contact` (`contactId`) USING BTREE,
								 INDEX `FK_contact_state_state` (`stateId`) USING BTREE,
								 CONSTRAINT `FK_contact_state_contact` FOREIGN KEY (`contactId`) REFERENCES `contact` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
								 CONSTRAINT `FK_contact_state_state` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;

ALTER TABLE `service_localization`
DROP COLUMN `nameTitle`,
	DROP COLUMN `nameAnchor`,
	DROP COLUMN `description`,
	DROP COLUMN `keywords`;
