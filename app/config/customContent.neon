services:
	-
		implement: App\FrontModule\Components\CustomContentRenderer\CustomContentRendererFactory
		inject: true
	- App\Model\CustomField\TextExtractor

extensions:
	cc: App\Model\CustomContent\CustomContentExtension

cc:
	definitions:
		content:
			type: group
			label: "Obsah"
			items:
				columns_divide:
					type: checkbox
					label: "Rozdělit do sloupců"
				content:
					type: tinymce
					label: "Obsah"
				spacing:
					@cf.definitions.spacing

		visit:
			type: group
			label: "Navštivte"
			items:
				title:
					type: text
					label: "Nadpis"
				content:
					type: tinymce
					label: "O<PERSON><PERSON> (nepovinné)"
				page:
					type: suggest
					subType: tree
					label: "Odkaz v rámci webu"
					url: @cf.suggestUrls.searchMutationPage
				btnText:
					type: text
					label: "Text tlačítka (defaulně název stránky)"
				image:
					type: image
					label: "Obrázek"
				full:
					type: checkbox
					label: "Fotografie na pozadí"
				inverse:
					type: checkbox
					label: "Inverzní barvy textu a tlačítka"
				spacing:
					@cf.definitions.spacing

		learn:
			type: group
			label: "Dozvědět se více"
			items:
				title:
					type: text
					label: "Nadpis"
				btnText:
					type: text
					defaultValue: "Learn more"
					label: "Text tlačítka"
				page:
					type: suggest
					subType: tree
					label: "Odkaz v rámci webu"
					url: @cf.suggestUrls.searchMutationPage
				image:
					type: image
					label: "Obrázek"
				spacing:
					@cf.definitions.spacing

		usefull:
			type: group
			label: "Užitečné články"
			items:
				title:
					type: text
					label: "Nadpis"
				page:
					type: suggest
					subType: tree
					label: "Odkaz v rámci webu"
					url: @cf.suggestUrls.searchMutationPage
				btnText:
					type: text
					label: "Text tlačítka"
				items:
					type: list
					label: "Články"
					items:
						page:
							type: suggest
							subType: blogLocalization
							label: "Výběr článku"
							url: @cf.suggestUrls.searchMutationBlogs
				spacing:
					@cf.definitions.spacing

		other_categories:
			type: group
			label: "Ostatní kategorie"
			items:
				spacing:
					@cf.definitions.spacing

		map:
			type: group
			label: "Mapa pobočky"
			items:
#				lng:
#					type: text
#					label: "Zeměpisná délka (longitude)"
#				lat:
#					type: text
#					label: "Zeměpisná výška (latitude)"
				spacing:
					@cf.definitions.spacing
		tips:
			type: group
			label: "Banner s tipy"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Obsah"
				bordered:
					type: checkbox
					label: "Ohraničení čarami"
				image:
					type: image
					label: "Ikona"
				items:
					type: list
					label: "Tlačítka"
					extends: @cf.definitions.globalLinkChoose
				spacing:
					extends: @cf.definitions.spacing


		content_image:
			type: group
			label: "Obrázek + obsah"
			items:
				image:
					type: image
					label: "Obrázek"
				ratio:
					type: select
					options: [
						{ label: "2:1", value: "2-1" },
						{ label: "3:2", value: "3-2" },
						{ label: "5:3", value: "5-3" },
						{ label: "5:7", value: "5-7" },
					]
					defaultValue: "5-7"
				content:
					type: tinymce
					label: "Obsah"
				spacing:
					@cf.definitions.spacing

		videos:
			type: group
			label: "Výpis videí"
			items:
				items:
					type: list
					label: "Položky"
					items:
						video:
							extends: @cf.definitions.video
						image:
							type: image
							label: "Zástupný obrázek pro video (viditelné před načtením videa)"
						annot:
							type: text
							label: "Popisek"

				spacing:
					@cf.definitions.spacing

		photos:
			type: group
			label: "Fotografie"
			items:
				title:
					type: text
					label: "Nadpis"
				rows:
					type: list
					label: "Řádky fotografií"
					hasContentToggle: true
					items:
						images:
							type: image
							multiple: true
							label: "Obrázky (vyberte počet dle zvoleného typu)"
						type:
							type: select
							label: "Typ zobrazení řádku"
							options: [
								{ label: "1 vlevo", value: "left" },
								{ label: "1 vlevo (menší)", value: "left-small" },
								{ label: "1 vpravo", value: "right" },
								{ label: "Malá a velká na výšku", value: "vertical-small-big" }
								{ label: "Velká a malá na výšku", value: "vertical-big-small" }
								{ label: "Malá a velká na šířku", value: "horizontal-small-big" }
								{ label: "Velká a malá na šířku", value: "horizontal-big-small" }
								{ label: "2 malé vertikální vlevo + na šířku vpravo", value: "2-vertical-left-horizontal-right" }
								{ label: "2 malé vertikální vpravo + na šířku vlevo", value: "2-vertical-right-horizontal-left" }
								{ label: "2 vertikální", value: "2-vertical" }
							]
							defaultValue: "left"

						text1:
							type: textarea
							label: "Popisek 1 (nepovinné)"
						text2:
							type: textarea
							label: "Popisek 2 (nepovinné, dle typu zobrazení)"


				# new
				# images:
				# 	label: "Výběr obrázků"
				# 	type: image
				# 	multiple: true
				# variant:
				# 	type: select
				# 	label: "Varianta rozložení"
				#	defaultValue: "r1",
				# 	options: [
				# 		{ label: "Produkt", value: "p1" },
				# 		{ label: "Reference 1", value: "r1" },
				# 		{ label: "Reference 2", value: "r2" }
				# 	]
				spacing:
					@cf.definitions.spacing

		content_photos:
			type: group
			label: "Fotografie s obsahem"
			items:
				type:
					type: select
					label: "Typ výpisu"
					options: [
						{ label: "Varianta 1", value: "1" },
						{ label: "Varianta 2", value: "2" },
					]
					defaultValue: "1"
				rows:
					type: list
					label: "Řádky fotografií s obsahem"
					items:
						images:
							type: image
							multiple: true
							label: "Obrázky"
						images_on_slide:
							type: select
							label: "Počet obrázků na slidu"
							options: [
								{ label: "1", value: "1" },
								{ label: "2", value: "2" },
								{ label: "3", value: "3" },
							]
							defaultValue: "1"
						title:
							type: text
							label: "Nadpis"
						content:
							type: textarea
							label: "Obsah"
				spacing:
					@cf.definitions.spacing

		newsletter:
			type: group
			label: "Newsletter"
			items:
				title:
					type: textarea
					label: "Nadpis"
				spacing:
					@cf.definitions.spacing

		newsletter_visit:
			type: group
			label: "Newsletter + Navštivte"
			items:
				title:
					type: textarea
					label: "Nadpis newsletteru"
				title_visit:
					type: text
					label: "Nadpis v pravém bloku"
				page:
					type: suggest
					subType: tree
					label: "Odkaz v rámci webu"
					url: @cf.suggestUrls.searchMutationPage
				btnText:
					type: text
					label: "Text tlačítka (defaulně název stránky)"
				image:
					type: image
					label: "Obrázek"
				spacing:
					@cf.definitions.spacing

		contact_form:
			type: group
			label: "Kontaktní formulář"
			items:
				title:
					type: textarea
					label: "Nadpis"
				content:
					type: textarea
					label: "Obsah"
				recipient:
					type: text
					label: "Email příjemce (pokud není, použije se \"CONTACT FORM - E-MAIL RECEIVER\")"
				hasRegions:
					type: checkbox
					label: "Zobrazit regiony"
				attachmentButtonText:
					type: text
					label: "Text tlačítka pro vložení přílohy"
				allowAttachmentAll:
					type: checkbox
					label: "Povolit nahrání všech typů souborů"
				spacing:
					@cf.definitions.spacing

		service_contact_form:
			type: group
			label: "Servisní kontaktní formulář"
			items:
				title:
					type: textarea
					label: "Nadpis"
				content:
					type: textarea
					label: "Obsah"
				recipient:
					type: text
					label: "Email příjemce (pokud není, použije se \"CONTACT FORM - E-MAIL RECEIVER\")"
				attachmentButtonText:
					type: text
					label: "Text tlačítka pro vložení přílohy"
				allowAttachmentAll:
					type: checkbox
					label: "Povolit nahrání všech typů souborů"
				spacing:
					@cf.definitions.spacing

		quote:
			type: group
			label: "Citace"
			items:
				highlighted:
					type: checkbox
					label: "Zvýraznění (hnědé podbarvení)"
				image:
					type: image
					label: "Obrázek"
				video:
					extends: @cf.definitions.video
				vertical:
					type: checkbox
					label: "Obrázek / video na výšku"
				author:
					type: text
					label: "Autor"
				position:
					type: text
					label: "Pozice"
				title:
					type: text
					label: "Nadpis nad citací"
				content:
					type: textarea
					label: "Citace"
				content2:
					type: tinymce
					label: "Obsah pod citací"
				isWideIllust:
					type: checkbox
					label: "Široká ilustrace"
				isReversed:
					type: checkbox
					label: "Obrácené pořadí"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Tlačítko"
				spacing:
					@cf.definitions.spacing

		quotes:
			type: group
			label: "Výpis citací na výšku"
			items:
				items:
					type: list
					label: "Citace"
					items:
						highlighted:
							type: checkbox
							label: "Zvýraznění (hnědé podbarvení)"
						image:
							type: image
							label: "Obrázek"
						video:
							extends: @cf.definitions.video
						author:
							type: text
							label: "Autor"
						position:
							type: text
							label: "Pozice"
						content:
							type: textarea
							label: "Citace"
						content2:
							type: tinymce
							label: "Obsah pod citací"
						btn:
							extends: @cf.definitions.linkChoose
							label: "Tlačítko"
				spacing:
					@cf.definitions.spacing

		content_quote:
			type: group
			label: "Citace + obsah"
			items:
				quote:
					type: group
					label: "Citace"
					items:
						highlighted:
							type: checkbox
							label: "Zvýraznění (hnědé podbarvení)"
						image:
							type: image
							label: "Obrázek"
						video:
							extends: @cf.definitions.video
						author:
							type: text
							label: "Autor"
						position:
							type: text
							label: "Pozice"
						content:
							type: textarea
							label: "Citace"
						title:
							type: text
							label: "Nadpis nad citací"
				content:
					type: tinymce
					label: "Obsah"
				spacing:
					@cf.definitions.spacing

		trademark:
			type: group
			label: "Ochranná známka"
			items:
				image:
					label: "Obrázek vlevo"
					type: image
				quote:
					type: group
					label: "Citace"
					items:
						highlighted:
							type: checkbox
							label: "Zvýraznění (hnědé podbarvení)"
						image:
							type: image
							label: "Obrázek"
						video:
							extends: @cf.definitions.video
						author:
							type: text
							label: "Autor"
						position:
							type: text
							label: "Pozice"
						content:
							type: textarea
							label: "Citace"
						btn:
							extends: @cf.definitions.linkChoose
							label: "Tlačítko"
				spacing:
					@cf.definitions.spacing
		separator:
			type: group
			label: "Horizontální oddělovač"
			items:
				spacing:
					@cf.definitions.spacing

		news:
			type: group
			label: "Novinky"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Manuální výběr článků"
					items:
						page:
							type: suggest
							subType: blogLocalization
							label: "Výběr článku"
							url: @cf.suggestUrls.searchMutationBlogs
				news:
					type: checkbox
					label: "Automatický výpis článků pro homepage"
				spacing:
					@cf.definitions.spacing

		video:
			type: group
			label: "Video"
			items:
				author:
					type: text
					label: "Autor"
				position:
					type: text
					label: "Pozice"
				title:
					type: text
					label: "Nadpis"
				poster:
					type: image
					label: "Zástupný obrázek (volitelné)"
				video:
					extends: @cf.definitions.video
				spacing:
					@cf.definitions.spacing

		btn:
			type: group
			label: "Tlačítko"
			items:
				btn:
					extends: @cf.definitions.linkChoose
				icon:
					type: select
					options: [
						{ label: "Bez ikony", value: "none" },
						{ label: "Šipka vlevo", value: "arrow-left" },
						{ label: "Šipka vpravo", value: "arrow-right" },
					]
					defaultValue: "none"
				spacing:
					@cf.definitions.spacing

		products_carousel:
			type: group
			label: "Carousel produktů"
			items:
				title:
					type: text
					label: "Nadpis"
				toggle:
					type: radio
					options: [
						{ label: "Ručně navolené produkty", value: "items" },
						{ label: "Využité produkty (pouze detail reference)", value: "in_use" },
					]
				items:
					type: list
					label: "Výběr produktů"
					items:
						product:
							type: suggest
							subType: product
							label: "Připojený produkt"
							url: @cf.suggestUrls.searchProductInMutation
				bordered:
					type: checkbox
					label: "Ohraničení čarami"
					defaultValue: true
				spacing:
					@cf.definitions.spacing

		info:
			type: group
			label: "Informace o produktu"
			items:
				image:
					type: image
					label: "Ilustrační obrázek (u parametrů)"
				downloads:
					type: group
					label: "Downloads"
					items:
						basic:
							type: list
							label: "Basic Materials"
							items:
								file:
									type: file
									label: "Soubor"
								text:
									type: text
									label: "Text tlačítka"
						premium:
							type: list
							label: "Premium Materials"
							items:
								file:
									type: file
									label: "Soubor"
								text:
									type: text
									label: "Text tlačítka"
				care:
					type: list
					label: "Product care"
					items:
						product:
							type: suggest
							subType: product
							label: "Připojený produkt"
							url: @cf.suggestUrls.searchProductInMutation
				tips:
					type: list
					label: "Maintance tips"
					items:
						page:
							type: suggest
							subType: blogLocalization
							label: "Výběr článku"
							url: @cf.suggestUrls.searchMutationBlogs
						text:
							type: text
							label: "Text tlačítka (nepovinné)"
				spacing:
					@cf.definitions.spacing

		references_carousel:
			type: group
			label: "Carousel referencí"
			items:
				toggle:
					type: radio
					options: [
						{ label: "Ručně navolené reference", value: "items" },
						{ label: "Ručně navolené reference + automatické doplnění do počtu (pouze detail reference)", value: "items_automatic" },
					]
				visibleCount:
					type: select
					label: "Počet viditelných referencí"
					options: [
						{ label: "3", value: 3 },
						{ label: "4", value: 4 },
					]
					defaultValue: 3
				items:
					type: list
					label: "Výběr referencí"
					items:
						reference:
							label: "Reference"
							type: suggest
							subType: referenceLocalization
							url: @cf.suggestUrls.searchReferenceLocalization
				showBtn:
					type: checkbox
					label: "Zobrazit tlačítko pro přechod na výpis"
				spacing:
					@cf.definitions.spacing

		images:
			type: group
			label: "Carousel fotografií"
			items:
				images:
					type: image
					multiple: true
					label: "Fotografie"
				horizontal:
					type: checkbox
					label: "Horizontální zobrazení (+ zobrazení po 3)"
				spacing:
					@cf.definitions.spacing

		how_it_works:
			type: group
			label: "Jak to funguje"
			items:
				title:
					type: text
					label: "Nadpis"
				image:
					type: image
					label: "Obrázek"
				content:
					type: tinymce
					label: "Obsah"

		video_content:
			type: group
			label: "Obsah s videem/fotkou"
			items:
				title:
					type: text
					label: "Nadpis"
				content:
					type: textarea
					label: "Obsah"
				video:
					extends: @cf.definitions.video
				photo:
					type: image
					label: "Obrázek / zástupný obrázek videa"
				spacing:
					@cf.definitions.spacing

		before_after:
			type: group
			label: "Srovnání historie"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Anotace"
				image:
					type: image
					label: "Ilustrační obrázek"
				photos:
					type: group
					label: "Ukázky práce"
					items:
						before:
							type: group
							label: "Před"
							items:
								image:
									type: image
									label: "Před"
								label:
									type: text
									label: "Popisek (letopočet)"
						after:
							type: group
							label: "Po"
							items:
								image:
									type: image
									label: "Po"
								label:
									type: text
									label: "Popisek (letopočet)"
				spacing:
					@cf.definitions.spacing

		book:
			type: group
			label: "Kniha"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Anotace"
				image:
					type: image
					label: "Ilustrační obrázek"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Tlačítko"
				spacing:
					@cf.definitions.spacing

		certificates:
			type: group
			label: "Certifikáty"
			items:
				title:
					type: text
					label: "Nadpis"
				certificates:
					type: list
					label: "Certifikáty"
					items:
						title:
							type: text
							label: "Nadpis"
						image:
							type: image
							label: "Obrázek"
				spacing:
					@cf.definitions.spacing

		files:
			type: group
			label: "Soubory ke stažení"
			items:
				title:
					type: text
					label: "Nadpis"
				files:
					type: file
					label: "Soubor"
					multiple: true
				spacing:
					@cf.definitions.spacing

		contact_person:
			type: group
			label: "Kontaktní osoba"
			items:
				title:
					type: text
					label: "Nadpis"
				person:
					label: "Výběr kontaktní osoby"
					type: suggest
					subType: contactLocalization
					url: @cf.suggestUrls.searchContactLocalizations
				spacing:
					@cf.definitions.spacing

		workers:
			type: group
			label: "Pracovníci"
			items:
				title:
					type: text
					label: "Nadpis"
				workers:
					type: list
					items:
						title:
							type: text
							label: "Název pozice"
						annot:
							type: textarea
							label: "Obsah"
						image:
							type: image
							label: "Obrázek"
				spacing:
					@cf.definitions.spacing

		chairs:
			type: group
			label: "Historie"
			items:
				title:
					type: text
					label: "Nadpis"
				chairs:
					type: list
					label: "Položky"
					items:
						year:
							type: text
							label: "Letopočet"
						title:
							type: text
							label: "Nadpis"
						annot:
							type: textarea
							label: "Obsah"
						image:
							label: "Vlastí obrázek (3:2)"
							type: image
						link:
							type: group
							label: "Odkaz"
							items:
								product:
									type: suggest
									subType: product
									label: "Připojený produkt"
									url: @cf.suggestUrls.searchProductInMutation
								page:
									type: suggest
									subType: tree
									label: "Odkaz v rámci webu"
									url: @cf.suggestUrls.searchMutationPage
								external:
									type: text
									label: "Externí odkaz (nezapomeňte na https://)"

				spacing:
					@cf.definitions.spacing

		experience_day:
			type: group
			label: "Zážitkový den"
			items:
				image:
					type: image
					label: "Obrázek"
				content:
					type: tinymce
					label: "Obsah"
				reversed:
					type: checkbox
					label: "Převrátit"
				middleAligned:
					type: checkbox
					label: "Vycentrový obsah"
				spacing:
					@cf.definitions.spacing

		about_content:
			type: group
			label: "About - obsah s 2 obrázky"
			items:
				title:
					type: text
					label: "Nadpis"
				author:
					type: text
					label: "Autor"
				annot:
					type: textarea
					label: "Obsah"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Tlačítko"
				small_image:
					type: image
					label: "Malý obrázek"
				large_image:
					type: image
					label: "Velký obrázek"
				reversed:
					type: checkbox
					label: "Převrátit"
				spacing:
					@cf.definitions.spacing

		about_grid:
			type: group
			label: "About - grid"
			items:
				energy:
					type: group
					label: "Energie"
					items:
						title:
							type: text
							label: "Nadpis"
						annot:
							type: textarea
							label: "Obsah"
				quote:
					label: "Citace"
					extends: @cc.definitions.quote
				article:
					type: group
					label: "Článek"
					items:
						title:
							type: text
							label: "Nadpis"
						annot:
							type: textarea
							label: "Obsah"
						image:
							type: image
							label: "Obrázek"
						btn:
							extends: @cf.definitions.linkChoose
							label: "Tlačítko"
				article2:
					type: group
					label: "Článek 2"
					items:
						title:
							type: text
							label: "Nadpis"
						annot:
							type: textarea
							label: "Obsah"
						image:
							type: image
							label: "Obrázek"
						btn:
							extends: @cf.definitions.linkChoose
							label: "Tlačítko"
				spacing:
					@cf.definitions.spacing

		product_header:
			type: group
			label: "Produktová hlavička"

		crossroad:
			type: group
			label: "Rozcestník"
			items:
				title:
					type: text
					label: "Nadpis"
				small_items:
					label: "Velikost položek"
					type: group
					items:
						isSmallWidth:
							type: "checkbox"
							label: "4 dlaždice"
				items:
					type: list

					items:
						image:
							type: image
							label: "Obrázek"
						link:
							hasContentToggle: true
							type: group
							label: "Odkaz"
							items:
								toggle:
									type: radio
									inline: true
									isContentToggle: true
									defaultValue: 'systemHref'
									options: [
										{ label: "Systémová stránka", value: "systemHref" },
										{ label: "Vlastní odkaz", value: "customHref" },
										{ label: "Soubor", value: "fileHref" },
									]
								systemHref:
									type: group
									items:
										page:
											type: suggest
											label: "Stránka"
											subType: tree
											url: @cf.suggestUrls.searchMutationPage
										hrefName:
											type: text
											label: "Text odkazu (volitelné)"
								customHref:
									type: group
									items:
										href:
											type: text
											label: "Odkaz"
										hrefName:
											type: text
											label: "Text odkazu"
								fileHref:
									type: group
									items:
										file:
											type: file
											label: "Soubor"
				spacing:
					@cf.definitions.spacing

	components:
		info:
			icon: "download"
			template: "info"
			definition: @cc.definitions.info
			category: "Ostatní"
		images:
			icon: "images"
			template: "images"
			definition: @cc.definitions.images
			category: "Media"
		references_carousel:
			icon: "suitcase"
			template: "references_carousel"
			definition: @cc.definitions.references_carousel
			category: "Ostatní"
		products_carousel:
			icon: "chair"
			template: "products_carousel"
			definition: @cc.definitions.products_carousel
			category: "Ostatní"
		otherCategories:
			icon: "folder"
			template: "other_categories"
			definition: @cc.definitions.other_categories
			category: "Ostatní"
		visit:
			icon: "greater-than"
			template: "visit"
			definition: @cc.definitions.visit
			category: "Ostatní"
		newsletter_visit:
			icon: "greater-than"
			template: "newsletter_visit"
			definition: @cc.definitions.newsletter_visit
			category: "Ostatní"
		learn:
			icon: "greater-than"
			template: "learn"
			definition: @cc.definitions.learn
			category: "Ostatní"
		tips:
			icon: "chair"
			template: "tips"
			definition: @cc.definitions.tips
			category: "Ostatní"
		content_quote:
			icon: "quote-right"
			template: "content_quote"
			definition: @cc.definitions.content_quote
			category: "Obsah"
		content_image:
			icon: "image"
			template: "content_image"
			definition: @cc.definitions.content_image
			category: "Obsah"
		photos:
			icon: "images"
			template: "photos"
			definition: @cc.definitions.photos
			category: "Media"
		videos:
			icon: "images"
			template: "videos"
			definition: @cc.definitions.videos
			category: "Media"
		content:
			icon: "paragraph"
			template: "content"
			definition: @cc.definitions.content
			category: "Obsah"
		newsletter:
			icon: "envelope"
			template: "newsletter"
			definition: @cc.definitions.newsletter
			category: "Ostatní"
		quote:
			icon: "quote-right"
			template: "quote"
			definition: @cc.definitions.quote
			category: "Obsah"
		quotes:
			icon: "quote-right"
			template: "quotes"
			definition: @cc.definitions.quotes
			category: "Obsah"
		trademark:
			icon: "stamp"
			template: "trademark"
			definition: @cc.definitions.trademark
			category: "Obsah"
		content_photos:
			icon: "chair"
			template: "content_photos"
			definition: @cc.definitions.content_photos
			category: "Obsah"
		contact_form:
			icon: "envelope"
			template: "contact_form"
			definition: @cc.definitions.contact_form
			category: "Ostatní"
		service_contact_form:
			icon: "envelope"
			template: "service_contact_form"
			definition: @cc.definitions.service_contact_form
			category: "Ostatní"

		separator:
			icon: "minus"
			template: "separator"
			definition: @cc.definitions.separator
			category: "Ostatní"
		news:
			icon: "paperclip"
			template: "news"
			definition: @cc.definitions.news
			category: "Ostatní"
		usefull:
			icon: "paperclip"
			template: "usefull"
			definition: @cc.definitions.usefull
			category: "Ostatní"
		video:
			icon: "play-circle"
			template: "video"
			definition: @cc.definitions.video
			category: "Media"
		btn:
			icon: "mouse-pointer"
			template: "btn"
			definition: @cc.definitions.btn
			category: "Ostatní"
		map:
			icon: "map"
			template: "map"
			definition: @cc.definitions.map
			category: "Ostatní"
		how_it_works:
			icon: "shipping-fast"
			template: "how_it_works"
			definition: @cc.definitions.how_it_works
			category: "Ostatní"
		video_content:
			icon: "play-circle"
			template: "video_content"
			definition: @cc.definitions.video_content
			category: "Obsah"
		before_after:
			icon: "image"
			template: before_after
			definition: @cc.definitions.before_after
			category: "Ostatní"
		book:
			icon: "book-open"
			template: book
			definition: @cc.definitions.book
			category: "Ostatní"
		certificates:
			icon: "image"
			template: certificates
			definition: @cc.definitions.certificates
			category: "Ostatní"
		files:
			icon: "file"
			template: files
			definition: @cc.definitions.files
			category: "Obsah"
		contact_person:
			icon: "envelope"
			template: contact_person
			definition: @cc.definitions.contact_person
			category: "Ostatní"
		workers:
			icon: "image"
			template: workers
			definition: @cc.definitions.workers
			category: "Ostatní"
		chairs:
			icon: "chair"
			template: chairs
			definition: @cc.definitions.chairs
			category: "Ostatní"
		experience_day:
			icon: "paragraph"
			template: experience_day
			definition: @cc.definitions.experience_day
			category: "Obsah"
		about_content:
			icon: "image"
			template: about_content
			definition: @cc.definitions.about_content
			category: "Obsah"
		about_grid:
			icon: "paragraph"
			template: about_grid
			definition: @cc.definitions.about_grid
			category: "Obsah"
		crossroad:
			icon: "sign-in-alt"
			template: crossroad
			definition: @cc.definitions.crossroad
			category: "Ostatní"


	templates:
		":Front:Homepage:default": *
		":Front:Page:default": *
		":Front:Page:about": *
		":Front:Page:aboutDetail": *
		":Front:Page:service": *
		":Front:Page:services": *

		":Front:Catalog:category": *
		":Front:Catalog:default": *
		":Front:Catalog:b2bCategory": *
		":Front:Catalog:rtsCategory": *
		":Front:Product:detail": *

		":Reference:Front:Reference:default": *
		":Reference:Front:Reference:detail": *

		":Career:Front:Career:default": *

		":Contact:Front:Contact:default": *

		":Front:User:marketingMaterials": *
		":Front:User:projectReservation": *
		":Front:User:pressAndMedia": *

		":Front:Catalog:rts": *




