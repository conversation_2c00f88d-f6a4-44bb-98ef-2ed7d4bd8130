{block content}

	<div class="u-pt-3xl">
		<div class="row-main">
			<h1 class="h0">
				Typografie
			</h1>
			<h2>
				Heading h2
			</h2>
			<p>
				Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.
			</p>
			<p>
				Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<figure>
				<img src="/static/img/illust/sample.jpg" width="400" height="250" alt="">
				<figcaption>
					Image description
				</figcaption>
			</figure>
			<h3>
				Heading h3
			</h3>
			<ul>
				<li>
					<strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
			</ul>
			<ol>
				<li>
					<strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
				<li>
					<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
				</li>
				<li>
					<strong>Duis gravida</strong> tincidunt enim sed cursus.
				</li>
			</ol>
			<p>
				Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.
			</p>
			<p>
				Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<blockquote>
				<p>
					<strong>Blockquote</strong> – Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolorum quia ipsa corrupti temporibus ratione voluptatibus, voluptatem eos culpa a, numquam suscipit deleniti veniam libero. Dicta soluta sint, officiis enim voluptate.
				</p>
			</blockquote>
			<p>
				Mirum est notare quam littera gothica, quam nunc putamus parum claram, anteposuerit litterarum formas humanitatis per seacula quarta decima et quinta decima. Eodem modo typi, qui nunc nobis videntur parum clari, fiant sollemnes in futurum.<br>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam <strong>liber tempor cum soluta nobis</strong> eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<h4>
				Heading 4 úrovně
			</h4>
			<ol>
				<li>
					Lorem ipsum dolor sit amet.
					<ol>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.
						</li>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?
						</li>
					</ol>
				</li>
				<li>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
				</li>
				<li>
					tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
				</li>
				<li>
					cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
				</li>
			</ol>
			<ul>
				<li>
					Lorem ipsum dolor sit amet.
					<ul>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.
						</li>
						<li>
							Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?
						</li>
					</ul>
				</li>
				<li>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
				</li>
				<li>
					tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
				</li>
				<li>
					cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
				</li>
			</ul>
			<hr>
			<p>
				Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
			</p>
			<dl>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
			</dl>
			<h5>
				Heading 5 úrovně
			</h5>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
			</p>
			<h6>
				Heading 6 úrovně
			</h6>
			<p>
				<cite>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</cite>
			</p>
			<h2>
				Placeholdery v textu
			</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
			</p>
			{* {include '../part/box/placeholder-article.latte'} *}
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
			</p>
			{* {include '../part/box/placeholder-product.latte'} *}
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
			</p>
			<h2>
				Tabular data
			</h2>
			<div class="u-table-responsive">
				<table>
					<caption>
						Table Caption
					</caption>
					<thead>
						<tr>
							<th>
								Table Heading 1
							</th>
							<th>
								Table Heading 2
							</th>
							<th>
								Table Heading 3
							</th>
							<th>
								Table Heading 4
							</th>
							<th>
								Table Heading 5
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
					</tbody>
					<tfoot>
						<tr>
							<th>
								Table Footer 1
							</th>
							<th>
								Table Footer 2
							</th>
							<th>
								Table Footer 3
							</th>
							<th>
								Table Footer 4
							</th>
							<th>
								Table Footer 5
							</th>
						</tr>
					</tfoot>
				</table>
			</div>
			<h2>
				Headings
			</h2>
			<h1 class="h0">
				Heading 0
			</h1>
			<h1>
				Heading 1
			</h1>
			<h2>
				Heading 2
			</h2>
			<h3>
				Heading 3
			</h3>
			<h4>
				Heading 4
			</h4>
			<h5>
				Heading 5
			</h5>
			<h6>
				Heading 6
			</h6>


			<h2>
				Paragraphs
			</h2>
			<p>
				A paragraph (from the Greek paragraphos, “to write beside” or “written beside”) is a self-contained unit of a discourse in writing dealing with a particular point or idea. A paragraph consists of one or more sentences. Though not required by the syntax of any language, paragraphs are usually an expected part of formal writing, used to organize longer prose.
			</p>

			<h2>
				Blockquotes
			</h2>
			<blockquote>
				<p>
					A block quotation (also known as a long quotation or extract) is a quotation in a written document, that is set off from the main text as a paragraph, or block of text.
				</p>
				<p>
					It is typically distinguished visually using indentation and a different typeface or smaller size quotation. It may or may not include a citation, usually placed at the bottom.
				</p>
				<cite>
					<a href="#">
						Said no one, ever.
					</a>
				</cite>
			</blockquote>

			<h2>
				Lists
			</h2>

			<h3>
				Definition list
			</h3>
			<dl>
				<dt>
					Definition List Title
				</dt>
				<dd>
					This is a definition list division.
				</dd>
			</dl>

			<h3>
				Ordered List
			</h3>
			<ol>
				<li>
					List Item 1
				</li>
				<li>
					List Item 2
				</li>
				<li>
					List Item 3
				</li>
			</ol>

			<h3>
				Unordered List
			</h3>
			<ul>
				<li>
					List Item 1
				</li>
				<li>
					List Item 2
				</li>
				<li>
					List Item 3
				</li>
			</ul>

			<h2>
				Horizontal rules
			</h2>
			<hr>

			<h2>
				Tabular data
			</h2>
			<div class="u-table-responsive">
				<table>
					<caption>
						Table Caption
					</caption>
					<thead>
						<tr>
							<th>
								Table Heading 1
							</th>
							<th>
								Table Heading 2
							</th>
							<th>
								Table Heading 3
							</th>
							<th>
								Table Heading 4
							</th>
							<th>
								Table Heading 5
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
						<tr>
							<td>
								Table Cell 1
							</td>
							<td>
								Table Cell 2
							</td>
							<td>
								Table Cell 3
							</td>
							<td>
								Table Cell 4
							</td>
							<td>
								Table Cell 5
							</td>
						</tr>
					</tbody>
					<tfoot>
						<tr>
							<th>
								Table Footer 1
							</th>
							<th>
								Table Footer 2
							</th>
							<th>
								Table Footer 3
							</th>
							<th>
								Table Footer 4
							</th>
							<th>
								Table Footer 5
							</th>
						</tr>
					</tfoot>
				</table>
			</div>

			<h2>
				Code
			</h2>
			<p>
				<strong>Keyboard input:</strong> <kbd>Cmd</kbd>
			</p>
			<p>
				<strong>Inline code:</strong> <code>&lt;div&gt;code&lt;/div&gt;</code>
			</p>
			<p>
				<strong>Sample output:</strong> <samp>This is sample output from a computer program.</samp>
			</p>

			<h2>
				Pre-formatted text
			</h2>
			<pre>P R E F O R M A T T E D T E X T
				! " # $ % &amp; ' ( ) * + , - . /
				0 1 2 3 4 5 6 7 8 9 : ; &lt; = &gt; ?
				@ A B C D E F G H I J K L M N O
				P Q R S T U V W X Y Z [ \ ] ^ _
				` a b c d e f g h i j k l m n o
				p q r s t u v w x y z { | } ~ </pre>

			<h2>
				Inline elements
			</h2>
			<p>
				<a href="#">This is a text link</a>.
			</p>
			<p>
				<strong>Strong is used to indicate strong importance.</strong>
			</p>
			<p>
				<em>This text has added emphasis.</em>
			</p>
			<p>
				<del>This text is deleted</del> and <ins>This text is inserted</ins>.
			</p>
			<p>
				<s>This text has a strikethrough</s>.
			</p>
			<p>
				Superscript<sup>®</sup>.
			</p>
			<p>
				Subscript for things like H<sub>2</sub>O.
			</p>
			<p>
				Abbreviation: <abbr title="HyperText Markup Language">HTML</abbr>
			</p>
			<p>
				<q cite="https://developer.mozilla.org/en-US/docs/HTML/Element/q">This text is a short inline quotation.</q>
			</p>
			<p>
				<cite>This is a citation.</cite>
			</p>
			<p>
				The <dfn>dfn element</dfn> indicates a definition.
			</p>
			<p>
				The <mark>mark element</mark> indicates a highlight.
			</p>
			<p>
				The <var>variable element</var>, such as <var>x</var> = <var>y</var>.
			</p>
			<p>
				The time element: <time datetime="2013-04-06T12:32+00:00">2 weeks ago</time>
			</p>

			<h2>
				Formulářové prvky
			</h2>
			<h3>
				Message boxy
			</h3>
			<p class="message">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<p class="message message--ok">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<p class="message message--warning">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<p class="message message--error">
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam autem, tempora itaque cum fuga, dignissimos dolorum facere ut harum voluptas distinctio perferendis minima incidunt consequatur magni dicta, fugiat. Velit, officia.
			</p>
			<h3>
				Inputy
			</h3>
			<p>
				<label for="default" class="inp-label">
					Defaultní input s labelem
				</label>
				<span class="inp-fix">
					<input type="text" name="default" id="default" class="inp-text">
				</span>
			</p>
			<p>
				<label for="default" class="inp-label">
					Underline input s labelem
				</label>
				<span class="inp-fix">
					<input type="text" name="default" id="default" class="inp-text inp-text--underline" value="Mer...">
				</span>
			</p>
			<p>
				<label for="email" class="inp-label">
					E-mail input
				</label>
				<span class="inp-fix">
					<input type="email" name="email" id="email" class="inp-text">
				</span>
			</p>
			<p>
				<label for="password" class="inp-label">
					Password input
				</label>
				<span class="inp-fix">
					<input type="password" name="password" id="password" class="inp-text">
				</span>
			</p>
			<p>
				<label for="number" class="inp-label">
					Number input
				</label>
				<span class="inp-fix">
					<input type="number" name="number" id="number" class="inp-text">
				</span>
			</p>
			<p>
				<label for="number" class="inp-label">
					Rounded
				</label>
				<span class="inp-fix">
					<input type="text" name="text" id="text" class="inp-text inp-text--rounded">
				</span>
			</p>
			<p>
				<label for="tel" class="inp-label">
					Phone input
				</label>
				<span class="inp-fix">
					<input type="tel" name="tel" id="tel" class="inp-text">
				</span>
			</p>
			<p>
				<label for="number" class="inp-label">
					Number input
				</label>
				<span class="inp-fix">
					<input type="number" name="number" id="number" class="inp-text">
				</span>
			</p>
			<p>
				<label for="select" class="inp-label">
					Select
				</label>
				<span class="inp-fix">
					<select name="select" id="select" class="inp-select">
						<option value="">
							Vyberte hodnotu
						</option>
						<option value="1">
							Hodnota 1
						</option>
						<option value="2">
							Hodnota 2
						</option>
						<option value="3">
							Hodnota 3
						</option>
						<option value="4">
							Hodnota 4
						</option>
					</select>
				</span>
			</p>
			<p>
				<label for="textarea" class="inp-label">
					Textarea
				</label>
				<span class="inp-fix">
					<textarea name="textarea" id="textarea" class="inp-text" cols="40" rows="6"></textarea>
				</span>
			</p>
			<p>
				<label for="placeholder" class="inp-label u-vhide">
					Input s placeholderem a skrytým labelem
				</label>
				<span class="inp-fix">
					<input type="text" name="placeholder" id="placeholder" class="inp-text" placeholder="Input s placeholderem a skrytým labelem">
				</span>
			</p>
			<p>
				<label for="disabled" class="inp-label">
					Disabled defaultní input
				</label>
				<span class="inp-fix">
					<input type="text" name="disabled" id="disabled" class="inp-text" disabled>
				</span>
			</p>
			<p class="has-error">
				<label for="error" class="inp-label">
					Defaultní input s errorem
				</label>
				<span class="inp-fix">
					<input type="text" name="error" id="error" class="inp-text">
				</span>
			</p>
			<h2>
				Checkboxy
			</h2>
			<p>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Checkbox 1
					</span>
				</label>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Checkbox 2
					</span>
				</label>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" disabled>
					<span class="inp-item__text">
						Checkbox 3
					</span>
				</label>
				<label class="inp-item inp-item--checkbox has-error">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Checkbox 4
					</span>
				</label>
			</p>
			<h2>
				Radia
			</h2>
			<p>
				<label class="inp-item inp-item--radio">
					<input type="radio" name="radio" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Radio 1
					</span>
				</label>
				<label class="inp-item inp-item--radio">
					<input type="radio" name="radio" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Radio 2
					</span>
				</label>
				<label class="inp-item inp-item--radio">
					<input type="radio" name="radio" value="1" class="inp-item__inp" disabled>
					<span class="inp-item__text">
						Radio 3
					</span>
				</label>
				<label class="inp-item inp-item--radio has-error">
					<input type="radio" name="radio" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Radio 4
					</span>
				</label>
			</p>
			<p>
				<label class="inp-item inp-item--lg inp-item--radio">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Checkbox 1
					</span>
				</label>
				<label class="inp-item inp-item--lg inp-item--radio">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Checkbox 2
					</span>
				</label>
				<label class="inp-item inp-item--lg inp-item--radio">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" disabled>
					<span class="inp-item__text">
						Checkbox 3
					</span>
				</label>
				<label class="inp-item inp-item--lg inp-item--radio has-error">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Checkbox 4
					</span>
				</label>
			</p>
			<h2>
				item-icon
			</h2>
			<p>
				<span class="item-icon item-icon--xs u-fz-md">
					{('arrow-right')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						Before
					</span>
				</span>
			</p>
			<p>
				<span class="item-icon item-icon--xs item-icon--after u-fz-md">
					{('arrow-right')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						After
					</span>
				</span>
			</p>
			<p>
				<span class="item-icon">
					{('arrow-right')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						Before
					</span>
				</span>
			</p>
			<p>
				<span class="item-icon item-icon--after">
					{('arrow-right')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						After
					</span>
				</span>
			</p>
			<h2>
				Tlačítka
			</h2>
			<div class="u-bgc-green u-pt-lg full-width">
				<div class="row-main u-mb-lg">
					<p>
						<a href="#" class="btn btn--lg">
							<span class="btn__text">
								Primary
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--lg btn--transparent">
							<span class="btn__text">
								Primary transparent
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary btn--lg">
							<span class="btn__text">
								<span class="item-icon">
									{('arrow-right')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										Secondary
									</span>
								</span>
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary btn--lg btn--transparent">
							<span class="btn__text">
								<span class="item-icon">
									{('arrow-right')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										Secondary transparent
									</span>
								</span>
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn">
							<span class="btn__text">
								Odeslat
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary@md">
							<span class="btn__text">
								Secondary from tablet
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary@md btn--transparent">
							<span class="btn__text">
								Transparent secondary from tablet
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary btn--primary@md">
							<span class="btn__text">
								Primary from tablet
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary btn--primary@md btn--transparent">
							<span class="btn__text">
								Transparent primary from tablet
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary">
							<span class="btn__text">
								<span class="item-icon item-icon--after">
									{('arrow-down')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										Load more
									</span>
								</span>
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--sm">
							<span class="btn__text">
								Size sm
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--md">
							<span class="btn__text">
								Size md
							</span>
						</a>
					</p>
					<p>
						<a href="#" class="btn btn--secondary btn--sm">
							<span class="btn__text">
								<span class="item-icon">
									{('arrow-right')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										With icon
									</span>
								</span>
							</span>
						</a>
					</p>
					<p class="is-loading">
						<a href="#" class="btn btn--secondary btn--sm btn--loader">
							<span class="btn__text">
								<span class="item-icon">
									{('arrow-right')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										With icon
									</span>
								</span>
							</span>
						</a>
					</p>
				</div>
			</div>
			<h2>
				Pomocné třídy
			</h2>
			<div class="u-table-responsive">
				<table>
					<thead>
						<tr>
							<th>
								Třída
							</th>
							<th>
								Popis a ukázka
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<th>
								.u-c-yellow-dark
							</th>
							<td class="u-c-yellow-dark">
								Tmavě-žlutý text
							</td>
						</tr>
						<tr>
							<th>
								.u-c-gray
							</th>
							<td class="u-c-gray">
								Šedý text
							</td>
						</tr>
						<tr>
							<th>
								.u-c-red
							</th>
							<td class="u-c-red">
								Červený text
							</td>
						</tr>
						<tr>
							<th>
								.decor
							</th>
							<td class="decor">
								Dekorativní text
							</td>
						</tr>
						<tr>
							<th>
								.u-bgc-green
							</th>
							<td class="u-bgc-green">
								Zelené pozadí
							</td>
						</tr>
						<tr>
							<th>
								.u-bgc-yellow
							</th>
							<td class="u-bgc-yellow">
								Žluté pozadí
							</td>
						</tr>
						<tr>
							<th>
								.u-bgc-red
							</th>
							<td class="u-bgc-red">
								Červené pozadí
							</td>
						</tr>
						<tr>
							<th>
								.u-ta-l
							</th>
							<td class="u-ta-l">
								Zarovnání textu doleva
							</td>
						</tr>
						<tr>
							<th>
								.u-ta-r
							</th>
							<td class="u-ta-r">
								Zarovnání textu doprava
							</td>
						</tr>
						<tr>
							<th>
								.u-ta-c
							</th>
							<td class="u-ta-c">
								Zarovnání textu na střed
							</td>
						</tr>
						<tr>
							<th>
								.u-ta-j
							</th>
							<td class="u-ta-j">
								Zarovnání textu do bloku
							</td>
						</tr>
						<tr>
							<th>
								.u-whs-nw
							</th>
							<td>
								Nezalomitelný text
							</td>
						</tr>
						<tr>
							<th>
								.u-tt-l
							</th>
							<td class="u-tt-l">
								Malá písmena
							</td>
						</tr>
						<tr>
							<th>
								.u-tt-u
							</th>
							<td class="u-tt-u">
								Velká písmena
							</td>
						</tr>
						<tr>
							<th>
								.u-tt-c
							</th>
							<td class="u-tt-c">
								Velká pouze první písmena
							</td>
						</tr>
						<tr>
							<th>
								.u-fw-l
							</th>
							<td class="u-fw-l">
								Tenké písmo
							</td>
						</tr>
						<tr>
							<th>
								.u-fw-n
							</th>
							<td class="u-fw-n">
								Normální písmo
							</td>
						</tr>
						<tr>
							<th>
								.u-fw-b
							</th>
							<td class="u-fw-b">
								Tučné písmo
							</td>
						</tr>
						<tr>
							<th>
								.u-fs-i
							</th>
							<td class="u-fs-i">
								Kurzíva
							</td>
						</tr>
						<tr>
							<th>
								.u-line-clamp
							</th>
							<td>
								Ukončení dlouhého textu třemi tečkami
							</td>
						</tr>
						<tr>
							<th>
								.u-va-t
							</th>
							<td class="u-va-t">
								Zarovnání nahoru
							</td>
						</tr>
						<tr>
							<th>
								.u-va-m
							</th>
							<td class="u-va-m">
								Zarovnání na střed
							</td>
						</tr>
						<tr>
							<th>
								.u-va-b
							</th>
							<td class="u-va-b">
								Zarovnání dolů
							</td>
						</tr>
						<tr>
							<th>
								.u-fl-l
							</th>
							<td>
								Float vlevo
							</td>
						</tr>
						<tr>
							<th>
								.u-fl-r
							</th>
							<td>
								Float vpravo
							</td>
						</tr>
						<tr>
							<th>
								.u-clearfix
							</th>
							<td>
								Clearování floatů
							</td>
						</tr>
						<tr>
							<th>
								.u-vhide
							</th>
							<td>
								Skrytí obsahu (zůstává zobrazitelný čtečkám)
							</td>
						</tr>
						<tr>
							<th>
								.u-js-hide
							</th>
							<td>
								Skrytí obsahu (pouze při zapnutém javascriptu)
							</td>
						</tr>
						<tr>
							<th>
								.u-out
							</th>
							<td>
								Vypozicování obsahu mimo viewport
							</td>
						</tr>
						<tr>
							<th>
								.u-js-out
							</th>
							<td>
								Vypozicování obsahu mimo viewport (pouze při zapnutém javascriptu)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-n
							</th>
							<td>
								Skrytí obsahu
							</td>
						</tr>
						<tr>
							<th>
								.u-d-b
							</th>
							<td>
								Zobrazení obsahu
							</td>
						</tr>
						<tr>
							<th>
								.u-d-n@sm
							</th>
							<td>
								Skrytí obsahu na všech breakpointech větších než SM (většinou &lt; 480px)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-b@sm
							</th>
							<td>
								Zobrazení obsahu na všech breakpointech větších než SM (většinou &lt; 480px)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-n@md
							</th>
							<td>
								Skrytí obsahu na všech breakpointech větších než MD (většinou &lt; 750px)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-b@md
							</th>
							<td>
								Zobrazení obsahu na všech breakpointech větších než MD (většinou &lt; 750px)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-n@lg
							</th>
							<td>
								Skrytí obsahu na všech breakpointech větších než LG (většinou &lt; 1000px)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-b@lg
							</th>
							<td>
								Zobrazení obsahu na všech breakpointech větších než LG (většinou &lt; 1000px)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-n@xl
							</th>
							<td>
								Skrytí obsahu na všech breakpointech větších než XL (většinou &lt; 1200px)
							</td>
						</tr>
						<tr>
							<th>
								.u-d-b@xl
							</th>
							<td>
								Zobrazení obsahu na všech breakpointech větších než XL (většinou &lt; 1200px)
							</td>
						</tr>
						<tr>
							<th>
								.u-mb-0
							</th>
							<td>
								Spodní odsazení s nulovou velikostí
							</td>
						</tr>
						<tr>
						<tr>
							<th>
								.u-mb-sm
							</th>
							<td>
								Spodní odsazení v "sm" velikosti
							</td>
						</tr>
						<tr>
							<th>
								.u-mb-md
							</th>
							<td>
								Spodní odsazení v "md" velikosti
							</td>
						</tr>
						<tr>
							<th>
								.u-mb-lg
							</th>
							<td>
								Spodní odsazení v "lg" velikosti
							</td>
						</tr>
						<tr>
							<th>
								.u-mb-xl
							</th>
							<td>
								Spodní odsazení v "xl" velikosti
							</td>
						</tr>
						<tr>
							<th>
								.u-no-print
							</th>
							<td>
								Skrytí obsahu pro tisk
							</td>
						</tr>
					</tbody>
				</table>
			</div>


			<div class="u-maw-8-12">
				<h2>
					Showrooms
				</h2>
				{include $templates.'/part/crossroad/showrooms.latte'}

				<h2>
					Showrooms - změna gridu
				</h2>
				{include $templates.'/part/crossroad/showroom-persons.latte'}
			</div>

			{* <h2>
				Kontakt - boxy
			</h2> *}

			{* {include $templates.'/part/crossroad/bnr-contact.latte'} *}

			{* <h2>
				Press & Media
			</h2>

			<div class="u-ov-h">
				<div class="grid grid--x-lines grid--x-xl">
					<div class="grid__cell size--6-12@lg">
						{include $templates.'/part/box/person.latte'}
					</div>
					<div class="grid__cell grid__cell--eq size--6-12@lg">
						{php $btns = []}
						{php array_push($btns, (object) array('page' => $pages->registration))}
						{php array_push($btns, (object) array('page' => $pages->userLogin))}

						{include $templates.'/part/box/bnr.latte', class=>'u-pt-xxl@lg', iconSrc=>'/static/img/illust/suitcase.svg', title=>$translator->translate("press_description"), buttons=>$btns, spacingBottom=>false, spacingBottomLg=>false}
					</div>
				</div>
			</div> *}

			<h2>
				c-std
			</h2>
			<h3>
				Default varianta (ostatní kategorie ve výpisu)
			</h3>
			{include $templates . '/part/crossroad/std.latte', crossroad=>$pages->eshop->crossroad}
			<h3>
				Speciální varianta (katalog, homepage, BZ homepage)
			</h3>
			{include $templates . '/part/crossroad/std.latte', class=>'c-std--spec', crossroad=>$pages->eshop->crossroad}


			{* <h2>Parametry</h2>

			{include $templates . '/part/crossroad/parameters.latte'} *}

			<h2>
				Grid
			</h2>
			<div>
				<h2> Defaultní grid (mobil portrait) </h2>
				<div class="grid">
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--2-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--3-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--4-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--5-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--7-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--8-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
					</div>
					<div class="grid__cell size--4-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--9-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
					</div>
					<div class="grid__cell size--3-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--10-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
					</div>
					<div class="grid__cell size--2-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--11-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
					</div>
					<div class="grid__cell size--1-12">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
					</div>
				</div>
				<h2> Grid od breakpointu sm (mobil landscape) </h2>
				<div class="grid">
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--2-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--3-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--4-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--5-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--6-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--6-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--7-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--8-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
					</div>
					<div class="grid__cell size--4-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--9-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
					</div>
					<div class="grid__cell size--3-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--10-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
					</div>
					<div class="grid__cell size--2-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--11-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
					</div>
					<div class="grid__cell size--1-12@sm">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
					</div>
				</div>
				<h2> Grid od breakpointu md (tablet) </h2>
				<div class="grid">
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--2-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--3-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--4-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--5-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--6-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--6-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--7-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--8-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
					</div>
					<div class="grid__cell size--4-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--9-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
					</div>
					<div class="grid__cell size--3-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--10-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
					</div>
					<div class="grid__cell size--2-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--11-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
					</div>
					<div class="grid__cell size--1-12@md">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
					</div>
				</div>
				<h2> Grid od breakpointu lg (desktop) </h2>
				<div class="grid">
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--2-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--3-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--4-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--5-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--6-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--6-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--7-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--8-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
					</div>
					<div class="grid__cell size--4-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--9-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
					</div>
					<div class="grid__cell size--3-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--10-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
					</div>
					<div class="grid__cell size--2-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--11-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
					</div>
					<div class="grid__cell size--1-12@lg">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
					</div>
				</div>
				<h2> Grid od breakpointu xl (desktop – large) </h2>
				<div class="grid">
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell size--2-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--2-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--3-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--3-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--4-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--4-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--5-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--7-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--6-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--6-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
					</div>
					<div class="grid__cell size--7-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
					</div>
					<div class="grid__cell size--5-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
					</div>
					<div class="grid__cell size--8-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
					</div>
					<div class="grid__cell size--4-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
					</div>
					<div class="grid__cell size--9-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
					</div>
					<div class="grid__cell size--3-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
					</div>
					<div class="grid__cell size--10-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
					</div>
					<div class="grid__cell size--2-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
					</div>
					<div class="grid__cell size--11-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
					</div>
					<div class="grid__cell size--1-12@xl">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
					</div>
					<div class="grid__cell">
						<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
					</div>
				</div>
				<h2> Auto šířka buňek gridu </h2>
				<div class="grid">
					<div class="grid__cell size--auto">
						<p> Lorem ipsum </p>
					</div>
					<div class="grid__cell size--auto">
						<p> Dolor sit amet </p>
					</div>
					<div class="grid__cell size--auto">
						<p> Lorem ipsum </p>
					</div>
					<div class="grid__cell size--auto">
						<p> Dolor sit amet </p>
					</div>
					<div class="grid__cell size--auto">
						<p> Lorem ipsum </p>
					</div>
					<div class="grid__cell size--auto">
						<p> Dolor sit amet </p>
					</div>
					<div class="grid__cell size--auto">
						<p> Lorem ipsum </p>
					</div>
					<div class="grid__cell size--auto">
						<p> Dolor sit amet </p>
					</div>
				</div>
				<h2> Auto šírka buňky gridu s vyplněním celého řádku </h2>
				<div class="grid">
					<div class="grid__cell size--autogrow">
						<p> Lorem ipsum </p>
					</div>
					<div class="grid__cell size--autogrow">
						<p> Dolor sit amet </p>
					</div>
				</div>
				<h2> Horizontální zarovnání </h2>
				<div class="grid">
					<div class="grid__cell size--4-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní - left </strong>
						</p>
					</div>
				</div>
				<div class="grid grid--center">
					<div class="grid__cell size--4-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní - center </strong>
						</p>
					</div>
				</div>
				<div class="grid grid--right">
					<div class="grid__cell size--4-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní - right </strong>
						</p>
					</div>
				</div>
				<div class="grid grid--space-between">
					<div class="grid__cell size--3-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní - space-between </strong>
						</p>
					</div>
					<div class="grid__cell size--3-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor </p>
					</div>
				</div>
				<h2> Vertikální zarovnání </h2>
				<div class="grid">
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní – top </strong>
						</p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
					</div>
				</div>
				<div class="grid grid--middle">
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní – middle </strong>
						</p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
					</div>
				</div>
				<div class="grid grid--bottom">
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní – bottom </strong>
						</p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
					</div>
				</div>
				<h3> Vertikální zarovnání na buňkách </h3>
				<div class="grid grid--middle">
					<div class="grid__cell grid__cell--top size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní – top </strong>
							<br> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
						</p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
					</div>
				</div>
				<div class="grid">
					<div class="grid__cell grid__cell--middle size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní – middle </strong>
							<br> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
						</p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
					</div>
				</div>
				<div class="grid">
					<div class="grid__cell grid__cell--bottom size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;">
							<strong> Defaultní – bottom </strong>
							<br> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
						</p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
					</div>
				</div>
				<h2> Srovnání výšek následující prvku v buňce </h2>
				<div class="grid">
					<div class="grid__cell grid__cell--eq size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
					</div>
					<div class="grid__cell size--6-12">
						<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
					</div>
				</div>
				<h2> Pořadí sloupců </h2>
				<div class="grid">
					<div class="grid__cell size--1-12 order--12">
						<p style="background: #f0f0f0; padding: 2rem;"> 1 -&gt; 12 </p>
					</div>
					<div class="grid__cell size--1-12 order--11">
						<p style="background: #f0f0f0; padding: 2rem;"> 2 -&gt; 11 </p>
					</div>
					<div class="grid__cell size--1-12 order--10">
						<p style="background: #f0f0f0; padding: 2rem;"> 3 -&gt; 10 </p>
					</div>
					<div class="grid__cell size--1-12 order--9">
						<p style="background: #f0f0f0; padding: 2rem;"> 4 -&gt; 9 </p>
					</div>
					<div class="grid__cell size--1-12 order--8">
						<p style="background: #f0f0f0; padding: 2rem;"> 5 -&gt; 8 </p>
					</div>
					<div class="grid__cell size--1-12 order--7">
						<p style="background: #f0f0f0; padding: 2rem;"> 6 -&gt; 7 </p>
					</div>
					<div class="grid__cell size--1-12 order--9">
						<p style="background: #f0f0f0; padding: 2rem;"> 7 -&gt; 9 </p>
					</div>
					<div class="grid__cell size--1-12 order--5">
						<p style="background: #f0f0f0; padding: 2rem;"> 8 -&gt; 5 </p>
					</div>
					<div class="grid__cell size--1-12 order--4">
						<p style="background: #f0f0f0; padding: 2rem;"> 9 -&gt; 4 </p>
					</div>
					<div class="grid__cell size--1-12 order--3">
						<p style="background: #f0f0f0; padding: 2rem;"> 10 -&gt; 3 </p>
					</div>
					<div class="grid__cell size--1-12 order--2">
						<p style="background: #f0f0f0; padding: 2rem;"> 11 -&gt; 2 </p>
					</div>
					<div class="grid__cell size--1-12 order--1">
						<p style="background: #f0f0f0; padding: 2rem;"> 12 -&gt; 1 </p>
					</div>
				</div>
			</div>
		</div>
	</div>

{/block}

