{block content}
	{snippet content}
		<div class="row-main">
			<div class="u-mb-last-0 u-pt-lg u-mb-md u-mb-3xl@md">
				<p class="h0 u-ta-c u-maw-10-12 u-mx-auto u-mb-lg">
					{if $object->annotation}
						{$object->annotation|texy|noescape}
					{else}
						{$object->name}
					{/if}
				</p>

				<div class="u-maw-6-12 u-mx-auto u-mb-lg u-mb-3xl@md">
					{control b2bSuggest}
				</div>

				{include $templates.'/part/box/content.latte'}

				{if isset($pages->eshop)}
					{include $templates.'/part/crossroad/std.latte', class=>'c-std--spec', crossroad=>$pages->eshop->crossroad, spacingBottom=>'md', spacingBottomLg=>'2xl', 'inB2b' => true, fetchFirst=>true}
					<p class="u-ta-c u-mb-sm u-mb-lg@md">
						<a href="{plink $pages->eshop, 'inB2b' => true}" class="btn btn--secondary">
							<span class="btn__text">
								{_"btn_show_all_product"}
							</span>
						</a>
					</p>
					<hr class="u-mb-lg u-mb-2xl@md">
				{/if}

				<div class="u-mb-md">
					<div class="grid">
						{foreach $object->cf->crossroads as $c}
							{if isset($c->page) && $c->page instanceof App\Model\CustomField\LazyValue && $c->page->getEntity()}
								<div class="grid__cell size--6-12@md size--4-12@xl">
									{include $templates.'/part/box/color.latte', item=>$c}
								</div>
							{/if}
						{/foreach}
					</div>
				</div>

				{* *kategorie produktu*<br>
				{foreach $categories as $category}
					<a n:href="$category">
						{$category->name}
					</a>
				{/foreach} *}

				{control customContentRenderer}
			</div>
		</div>

	{/snippet}
{/block}
