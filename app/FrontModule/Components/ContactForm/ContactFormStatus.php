<?php declare(strict_types=1);

namespace App\FrontModule\Components\ContactForm;

use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\PostType\Contact\Model\Orm\ContactLocalizationRepository;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Collection\EmptyCollection;

class ContactFormStatus
{
	private ICollection $contactLocalizations;

	private readonly ICollection $states;
	private State $selectedState;

	public function __construct(
		private readonly StateRepository $stateRepository,
		private readonly ContactLocalizationRepository $contactLocalizationRepository,
		State $selectedState,
		public readonly ?bool $hasStateAndRegions = false,
		public readonly ?string $forcedRecipientEmail = null,
		public readonly ?string $type = 'contact',
		?ICollection $contactLocalizations = new EmptyCollection(),
		public readonly ?ContactLocalization $forcedContactPerson = null,
		?ICollection $states = new EmptyCollection(),
		public readonly ?array $stateIdToContactIds = [],
		public readonly ?string $forcedAttachmentButtonText = null,
		public readonly ?bool $forcedAllowAttachmentAll = null,
	)
	{
		$this->selectedState = $selectedState;
		if ($states?->count() === 0) {
			$states = $this->stateRepository->findByIds(array_keys($stateIdToContactIds));
		}

		if ($contactLocalizations?->count() === 0) {
			$contactLocalizations = $this->calculateContactLocalizations();
		} else {
			$contactLocalizations = $contactLocalizations->findBy([
				'contact->hideInContactForm' => 0,
			]);
		}

		assert($states instanceof ICollection);
		$this->states = $states;
		$this->contactLocalizations = $contactLocalizations;
	}

	public function getStates(): ICollection
	{
		return $this->states;
	}

	public function getContactLocalizations(): ICollection
	{
		return $this->contactLocalizations;
	}

	public function setState(int $stateId): void
	{
		$this->selectedState = $this->stateRepository->getById($stateId) ?? $this->selectedState;
		$contactLocalizations = $this->calculateContactLocalizations();
		$this->contactLocalizations = $contactLocalizations;
	}

	public function getState(): State
	{
		return $this->selectedState;
	}

	public function calculateContactLocalizations(): ICollection
	{
		$contactLocalizations = new EmptyCollection();
		if (isset($this->stateIdToContactIds[$this->selectedState->id])) {
			$contactLocalizations = $this->contactLocalizationRepository->findByIds($this->stateIdToContactIds[$this->selectedState->id]);
			$contactLocalizations = $contactLocalizations->findBy([
				'contact->hideInContactForm' => 0,
			]);
		}

		return $contactLocalizations;
	}
}
