<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ContactForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\FormHelpers\StateRegionHelper;
use App\Model\Form\CommonFormFactory;
use App\Model\Gtm\Gtm;
use App\Model\Gtm\GtmCareerSubmitEvent;
use App\Model\Gtm\GtmContactSubmitEvent;
use App\Model\Orm\Routable;
use App\PostType\Career\Model\Orm\CareerLocalization;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\PostType\Page\Model\Orm\Tree;
use App\Utils\HubspotFormService\HubspotFormService;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use App\Model\Email\Common;
use App\Model\Email\CommonFactory;
use App\FrontModule\Components\HasAntispamInput;
use App\FrontModule\Components\HasError500Catcher;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\TranslatorDB;
use Nette\Utils\Validators;
use Tracy\Debugger;


class ContactForm extends BaseContactForm
{

//	public static function getSelectorValues(TranslatorDB $translator, array|ICollection $contactLocalizations, ?State $selectedState = null): array
//	{
//		$values = [];
//		foreach ($contactLocalizations as $contactLocalization) {
//			$values[$contactLocalization->id] = $contactLocalization->name;
//			if ($selectedState instanceof State) {
//				$regions = $contactLocalization->getParent()->getRegionsByState($selectedState);
//				if ($regions !== '') {
//					$values[$contactLocalization->id] = $translator->translate($regions);
//				}
//			}
//		}
//
//		return $values;
//	}

	protected function createComponentForm(): UI\Form
	{
		$form = $this->commonFormFactory->create();
		$form->setTranslator($this->translator);

		$form->addText('name', 'form_label_name_and_surname')->setRequired();
		$form->addEmail('email', 'form_label_email')
			->setRequired();
		$form->addText('phone', 'form_label_telephone')
			->setRequired();
		$form->addTextArea('text', 'form_label_text')
			->setRequired();

		if ($this->contactFormStatus->type !== 'contact') {
			$form->addMultiUpload('files', 'form_label_photos');
		}

		if ($this->contactFormStatus->hasStateAndRegions && $this->contactFormStatus->getStates()->count() > 1) {
			$this->stateRegionHelper->addState($form, $this->object->getMutation(), $this->contactFormStatus->getState(), $this->contactFormStatus->getStates());
		}

		if ($this->contactFormStatus->getContactLocalizations()->count() > 0) {
			$this->stateRegionHelper->addContact($form, $this->contactFormStatus->getState(), $this->contactFormStatus->getContactLocalizations());
		}

		if ($this->text) {
			$form['text']->setDefaultValue($this->text);
		}

		$form->addSubmit('send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		$this->attachAntispamTo($form);

		return $form;
	}

	/**
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$values->type = $this->contactFormStatus->type;
			\assert($this->object instanceof Routable);
			$values->page = $this->object->getNameTitle();
			$values->pageLink = $this->linkFactory->linkTranslateToNette($this->object);
			$contactId = (isset($values->contact) ? (int) $values->contact : null);

			$replyTo = [
				$values->email,
				$values->name,
			];

			$recipient = $this->getCompanyRecipient($contactId);

			$this->commonEmail->send(
				from: '',
				to: $recipient,
				dbTemplate: 'contact',
				data: (array)$values,
				replyTo: $replyTo,
				files: ($values->files ?? []),
			);

			if ($this->contactFormStatus->type === 'career') {
				if ($this->object instanceof CareerLocalization) {
					$this->gtm->pushEvent((new GtmCareerSubmitEvent($this->gtm))->setup($this->object));
				}
			} else {
				$this->gtm->pushEvent((new GtmContactSubmitEvent($this->gtm))->setup());
			}

			// hubspot API call
			$hubSpotType = null;
			if ($this->contactFormStatus->type === 'contact') {
				$hubSpotType = HubspotFormService::FORM_TYPE_CONTACT;
			} else if ($this->object instanceof Tree AND $this->object->uid === 'servicesRepair') {
				$hubSpotType = HubspotFormService::FORM_TYPE_REPAIR;
			} else if ($this->contactFormStatus->type === 'repair') {
				$hubSpotType = HubspotFormService::FORM_TYPE_CUSTOMIZED;
			}
			if ($hubSpotType !== null) {
				/** @var ContactLocalization $contactLocalization */
			 	$contactLocalization = $this->contactFormStatus->getContactLocalizations()->getBy(['id' => $contactId]);
				$this->hubspotFormService->submitForm($hubSpotType, $values, $values->pageLink, $values->page, $this->contactFormStatus->getState(), $contactLocalization);
			}

			$this->flashMessage($this->okMessage, 'ok');

		} catch (\Throwable $e) {
			Debugger::log($e, 'error');
			$this->flashMessage('Operation failed', 'error');
		}

		if ($this->presenter->isAjax()) {
//			$form->setValues(array(), true);
			$form->setValues(array(
				'name' => '',
				'email' => '',
				'text' => '',
				'phone' => '',
			), false);
			$this->redrawControl('form');
			$this->presenter->redrawControl('gtmEvents');
		} else {
			$this->presenter->redirect('this');
		}
	}


	public function render(array $props = []): void
	{
		try {
			$this->template->title = isset($props['title']) && $props['title'] !== false ? $props['title'] : $this->translator->translate('form_title_default');
			$this->template->content = isset($props['content']) && $props['content'] !== false ? $props['content'] : null;
			$this->template->spacing = isset($props['spacing']) && $props['spacing'] !== false ? $props['spacing'] : null;
			$this->template->fileUploadText = $this->getUploadInputLabel();

			$this->template->setTranslator($this->translator);
			$this->template->object = $this->object;
			$this->template->type = $this->contactFormStatus->type;
			$this->template->forcedAllowAttachmentAll = $this->contactFormStatus->forcedAllowAttachmentAll;
			$this->template->contactLocalization = $this->contactFormStatus->forcedContactPerson;
			$this->template->pages = $this->mutationHolder->getMutation()->pages;
//			if ($this->presenter->isAjax()) {
//				$this->presenter->redrawControl();
//				$this->redrawControl('form');
//			}

			$this->template->render(__DIR__ . '/contactForm.latte');
		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}


	private function getCompanyRecipient(int $contactId = null): string
	{
		if ($this->contactFormStatus->forcedRecipientEmail !== null
			&& Validators::isEmail($this->contactFormStatus->forcedRecipientEmail)) {
			return $this->contactFormStatus->forcedRecipientEmail;
		}

		if ($contactId !== null
			&& ($selectedContactLocalization = $this->contactFormStatus->getContactLocalizations()->getById($contactId)) instanceof ContactLocalization
			&& isset($selectedContactLocalization->getParent()->cf->info->mail)
			&& Validators::isEmail($selectedContactLocalization->getParent()->cf->info->mail)
		) {
			return $selectedContactLocalization->getParent()->cf->info->mail;
		}

		if ($this->contactFormStatus->forcedContactPerson !== null
			&& isset($this->contactFormStatus->forcedContactPerson->getParent()->cf->info->mail)
			&& Validators::isEmail($this->contactFormStatus->forcedContactPerson->getParent()->cf->info->mail)
		) {
			return $this->contactFormStatus->forcedContactPerson->getParent()->cf->info->mail;
		}

		if ($this->contactFormStatus->type === 'career') {
			return $this->mutationHolder->getMutation()->hrEmail;
		}

		return $this->mutationHolder->getMutation()->contactEmail;
	}


	public function handleChangeCountry(int $countryId): void
	{
		$this->contactFormStatus->setState($countryId);
//		$this->loadRegions($countryId);
		if ($this->presenter->isAjax()) {
			$this->redrawControl('regions');
//			$this->redrawControl('zip');
		}
	}

}
