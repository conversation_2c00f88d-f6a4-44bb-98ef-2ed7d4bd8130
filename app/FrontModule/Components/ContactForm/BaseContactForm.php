<?php declare(strict_types=1);

namespace App\FrontModule\Components\ContactForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\FormHelpers\StateRegionHelper;
use App\FrontModule\Components\HasAntispamInput;
use App\FrontModule\Components\HasError500Catcher;
use App\Model\Email\Common;
use App\Model\Email\CommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Gtm\Gtm;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Routable;
use App\Model\TranslatorDB;
use App\Utils\HubspotFormService\HubspotFormService;
use Nette\Application\UI;

class BaseContactForm extends UI\Control
{
	use HasAntispamInput;
	use Has<PERSON>rror500Catcher;

	protected Common $commonEmail;

	protected string $text = '';

	protected string $okMessage;

	public function __construct(
		protected Routable $object,
		protected TranslatorDB $translator,
		protected MutationHolder $mutationHolder,
		protected LinkFactory $linkFactory,
		protected readonly CommonFactory $commonEmailFactory,
		protected readonly MessageForFormFactory $messageForFormFactory,
		protected readonly CommonFormFactory $commonFormFactory,
		protected readonly StateRegionHelper $stateRegionHelper,
		protected readonly ContactFormStatus $contactFormStatus,
		protected readonly Gtm $gtm,
		protected readonly HubspotFormService $hubspotFormService,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	public function init(): void
	{
		$this->commonEmail = $this->commonEmailFactory->create();
		$this->okMessage = 'form_' . $this->contactFormStatus->type . '_ok';

		if ($this->presenter->getRequest()->getPost('state') !== null) {
			$this->contactFormStatus->setState((int) $this->presenter->getRequest()->getPost('state'));
		}
	}

	public function getUploadInputLabel(): string
	{
		if ($this->contactFormStatus->forcedAttachmentButtonText) {
			return $this->contactFormStatus->forcedAttachmentButtonText;
		}

		if ($this->contactFormStatus->type === 'career') {
			return 'form_label_cv';
		}

		return 'form_label_photos';

	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}
}
