<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Suggest;

use App\Model\ElasticSearch\TimeLog\Repository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\ProductRepository;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class Suggest extends UI\Control
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly TranslatorDB $translator,
		private readonly Repository $esTimeLogRepository,
		private readonly ProductRepository $productRepository,
		private readonly bool $inB2b = false,
		private readonly bool $inB2bHP = false,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->pages = $this->mutation->pages;
		$this->template->inB2b = $this->inB2b;
		$this->template->inB2bHP = $this->inB2bHP;
		$this->template->searchTopList = $this->esTimeLogRepository->findMostPopularSearches($this->mutation);

		/*if ($search !== null) {
			$this->esTimeLogRepository->addDocument(['search' => mb_strtolower($search)], $this->mutation);
		}*/

		$this->template->render(__DIR__ . '/suggest.latte');
	}


	public function handleClickProductName(int $productId): void
	{
		$product = $this->productRepository->getById($productId);
		if ($product !== null) {
			$this->esTimeLogRepository->addNewProductSearch($product, $this->mutation);
		}
		$this->presenter->redirect($this->mutation->pages->search, ['search' => $product->nameAnchor, 'inB2b' => $this->inB2b]);
	}
}




