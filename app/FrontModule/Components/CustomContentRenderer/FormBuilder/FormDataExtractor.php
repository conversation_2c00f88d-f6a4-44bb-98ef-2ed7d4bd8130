<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer\FormBuilder;

/**
 * Extracts and validates form configuration data from custom content arrays
 */
readonly class FormDataExtractor
{
    /**
     * Extracts form configuration from ccDataArray
     *
     * @param array $ccDataArray The custom content data array
     * @param int $ccIndex The index to extract configuration from
     * @return FormData Configuration data with extracted values
     */
    public function extractFormData(array $ccDataArray, int $ccIndex): FormData
    {
        if (!isset($ccDataArray[$ccIndex][0])) {
            return FormData::createEmpty();
        }

        $data = $ccDataArray[$ccIndex][0];

        return new FormData(
            recipientEmail: $data->recipient ?? null,
            hasRegions: $data->hasRegions ?? false,
            allowAttachmentAll: $data->allowAttachmentAll ?? false,
            attachmentButtonText: $data->attachmentButtonText ?? null,
        );
    }

    /**
     * Prepares ccDataArray from object
     *
     * @param object $object The object containing cc data
     * @return array Prepared data array
     */
    public function prepareCcDataArray(object $object): array
    {
        if (!isset($object->cc)) {
            return [];
        }

        return array_values((array) $object->cc);
    }
}
