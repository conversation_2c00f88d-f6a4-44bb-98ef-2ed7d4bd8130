<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer\FormBuilder;

use App\FrontModule\Components\ConfigurationOrderForm\StateContactModel;
use App\FrontModule\Components\ContactForm\ContactForm;
use App\FrontModule\Components\ContactForm\ContactFormFactory;
use App\FrontModule\Components\ContactForm\ContactFormStatus;
use App\FrontModule\Components\ServiceContactForm\ServiceContactForm;
use App\FrontModule\Components\ServiceContactForm\ServiceContactFormFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use InvalidArgumentException;

/**
 * Builder class for creating contact forms with different configurations
 */
readonly class ContactFormBuilder
{
    public function __construct(
        private ContactFormFactory $contactFormFactory,
        private ServiceContactFormFactory $serviceContactFormFactory,
        private StateContactModel $stateContactModel,
        private Orm $orm,
    )
	{}

    /**
     * Creates a contact form based on configuration and form data
     *
     * @param Routable $object The routable object
     * @param State $state The current state
     * @param Mutation $mutation The current mutation
     * @param ContactFormConfig $config Form configuration
     * @param FormData $formData Extracted form data
     * @return mixed The created contact form
     */
    public function createContactForm(
        Routable $object,
        State $state,
        Mutation $mutation,
        ContactFormConfig $config,
        FormData $formData,
    ): ContactForm|ServiceContactForm
	{
        if (!$config->isValidFactoryType()) {
            throw new InvalidArgumentException("Invalid factory type: {$config->factoryType}");
        }

        $contactFormStatus = $this->createContactFormStatus(
            state: $state,
            mutation: $mutation,
            config: $config,
            formData: $formData,
        );

		return $this->selectFactory($config->factoryType)->create(
            $object,
            contactFormStatus: $contactFormStatus,
        );
    }

    private function createContactFormStatus(
        State $state,
        Mutation $mutation,
        ContactFormConfig $config,
        FormData $formData,
    ): ContactFormStatus
	{
        $stateIdToContactIds = $config->shouldUseRegions($formData->hasRegions)
            ? $this->stateContactModel->getCountryMapForMutation($mutation)
            : [];

        return new ContactFormStatus(
            stateRepository: $this->orm->state,
            contactLocalizationRepository: $this->orm->contactLocalization,
            selectedState: $state,
            hasStateAndRegions: $config->hasStateAndRegions,
            forcedRecipientEmail: $formData->recipientEmail,
            type: $config->type,
            stateIdToContactIds: $stateIdToContactIds,
            forcedAttachmentButtonText: $formData->attachmentButtonText,
            forcedAllowAttachmentAll: $formData->allowAttachmentAll,
        );
    }

    private function selectFactory(string $factoryType): ContactFormFactory|ServiceContactFormFactory
    {
		//TODO use constants if necessary?
        return match ($factoryType) {
            'contact' => $this->contactFormFactory,
            'service' => $this->serviceContactFormFactory,
            default => throw new InvalidArgumentException("Unsupported factory type: {$factoryType}"),
        };
    }
}
