<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer\FormBuilder;

/**
 * Data transfer object for form configuration extracted from custom content
 */
readonly class FormData
{
    public function __construct(
        public ?string $recipientEmail,
        public bool $hasRegions,
        public bool $allowAttachmentAll,
        public ?string $attachmentButtonText,
    ) {
    }

    public static function createEmpty(): self
    {
        return new self(
            recipientEmail: null,
            hasRegions: false,
            allowAttachmentAll: false,
            attachmentButtonText: null,
        );
    }

    public function hasRecipientEmail(): bool
    {
        return $this->recipientEmail !== null && $this->recipientEmail !== '';
    }

    public function hasAttachmentButtonText(): bool
    {
        return $this->attachmentButtonText !== null && $this->attachmentButtonText !== '';
    }
}
