<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer\FormBuilder;

/**
 * Configuration class for contact form creation
 * Encapsulates all parameters needed to create different types of contact forms
 */
readonly class ContactFormConfig
{
    public function __construct(
        public bool $hasStateAndRegions,
        public bool $useRegionsLogic,
        public string $factoryType,
        public string $type = 'repair',
    )
	{}

    public static function forServiceForm(): self
    {
        return new self(
            hasStateAndRegions: true,
            useRegionsLogic: true,
            factoryType: 'contact',
        );
    }

    public static function forServiceOrderForm(): self
    {
        return new self(
            hasStateAndRegions: false,
            useRegionsLogic: false,
            factoryType: 'service',
        );
    }

    public function shouldUseRegions(bool $hasRegionsInData): bool
    {
        return $this->useRegionsLogic && $hasRegionsInData;
    }

    public function isValidFactoryType(): bool
    {
        return in_array($this->factoryType, ['contact', 'service'], true);
    }
}
