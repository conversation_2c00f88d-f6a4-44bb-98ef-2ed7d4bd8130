<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer;

use App\FrontModule\Components\ConfigurationOrderForm\StateContactModel;
use App\FrontModule\Components\ContactForm\ContactFormFactory;
use App\FrontModule\Components\CustomContentRenderer\FormBuilder\ContactFormBuilder;
use App\FrontModule\Components\CustomContentRenderer\FormBuilder\ContactFormConfig;
use App\FrontModule\Components\CustomContentRenderer\FormBuilder\FormDataExtractor;
use App\FrontModule\Components\NewsletterForm\NewsletterFormFactory;
use App\FrontModule\Components\ProductParameters\ProductParameters;
use App\FrontModule\Components\ProductParameters\ProductParametersFactory;
use App\FrontModule\Components\ServiceContactForm\ServiceContactFormFactory;
use App\Infrastructure\Latte\Filters;
use App\Model\ConfigService;
use App\Model\CustomContent\CustomContent;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\Model\TranslatorDB;
use App\PostType\Reference\Model\AttachedReferences;
use Contributte\Application\UI\NullControl;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\IEntity;

/**
 * @property-read DefaultTemplate $template
 */
final class CustomContentRenderer extends UI\Control
{

	private readonly ContactFormBuilder $contactFormBuilder;
	private readonly FormDataExtractor $formDataExtractor;

	public function __construct(
		private readonly IEntity $object,
		private readonly Mutation $mutation,
		private readonly PriceLevel $priceLevel,
		private readonly State $state,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
		private readonly NewsletterFormFactory $newsletterFormFactory,
		private readonly ContactFormFactory $contactFormFactory,
		private readonly ServiceContactFormFactory $serviceContactFormFactory,
		private readonly AttachedReferences $attachedReferences,
		private readonly ProductParametersFactory $productParametersFactory,
		private readonly StateContactModel $stateContactModel,
		private readonly Orm $orm,
		private readonly CustomContent $customContent,
		private readonly ?ProductVariant $variant = null,
	)
	{
		$this->contactFormBuilder = new ContactFormBuilder(
			$this->contactFormFactory,
			$this->serviceContactFormFactory,
			$this->stateContactModel,
			$this->orm,
		);
		$this->formDataExtractor = new FormDataExtractor();
	}


	public function render(array $props = []): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->templates = FE_TEMPLATE_DIR;
		$template->defaultTemplateDirectory = FE_TEMPLATE_DIR . '/part/customContent';
		$template->isDev = $this->configService->get('isDev');
		$template->props = $props;
		$template->object = $this->object;
		$template->mutation = $this->mutation;
		$template->pages = $this->mutation->pages;
		$template->priceLevel = $this->priceLevel;
		$template->attachedReferences = $this->attachedReferences;
		$template->state = $this->state;
		$template->translator = $this->translator;
		assert(method_exists($this->object, 'getCcModules'));
		if ($this->object->getCcModules()) {
			assert(isset($this->object->cc));
			$template->defaultObjectCC = $this->object->cc ?? [];
		} else {
			$template->defaultObjectCC = [];
		}

		$isCheatsheet = ((isset($this->object->parent) && isset($this->object->parent->uid) && $this->object->parent->uid === "cheatsheet"));
		$this->template->isCheatsheet = $isCheatsheet;
		if ($isCheatsheet) {
			$this->template->allCustomComponentsLabels = $this->customContent->getAllCustomComponentsLabels((array) $this->object->getCcModules());
		} else {
			$this->template->allCustomComponentsLabels = [];
		}

		$template->addFilter('parseVideoId', Filters::parseVideoId(...));
		$template->render(__DIR__ . '/customContentRenderer.latte');
	}

	public function createComponentNewsletterForm(): UI\Multiplier
	{
		return new UI\Multiplier(function () {
			return $this->newsletterFormFactory->create($this->object);
		});
	}

	/**
	 * Creates a service form component
	 *
	 * @return UI\Multiplier Form multiplier component
	 */
	public function createComponentServiceForm(): UI\Multiplier
	{
		return new UI\Multiplier(function (string $ccIndex) {
			$ccIndex = (int) $ccIndex;

			$ccDataArray = $this->formDataExtractor->prepareCcDataArray($this->object);
			$formData = $this->formDataExtractor->extractFormData($ccDataArray, $ccIndex);
			$config = ContactFormConfig::forServiceForm();

			assert($this->object instanceof Routable);

			// Build and return the contact form
			return $this->contactFormBuilder->createContactForm(
				$this->object,
				$this->state,
				$this->mutation,
				$config,
				$formData
			);
		});
	}

	public function createComponentServiceOrderForm(): UI\Multiplier
	{

		return new UI\Multiplier(function (string $ccIndex) {
			$ccIndex = (int) $ccIndex;

			$ccDataArray = $this->formDataExtractor->prepareCcDataArray($this->object);
			$formData = $this->formDataExtractor->extractFormData($ccDataArray, $ccIndex);
			$config = ContactFormConfig::forServiceOrderForm();

			assert($this->object instanceof Routable);

			return $this->contactFormBuilder->createContactForm(
				$this->object,
				$this->state,
				$this->mutation,
				$config,
				$formData
			);
		});
	}

	public function createComponentProductParameters(): ProductParameters|NullControl
	{
		if ($this->object instanceof ProductLocalization && $this->variant !== null) {
			return $this->productParametersFactory->create($this->object->getParent(), $this->variant);
		}

		return new NullControl();
	}
}
