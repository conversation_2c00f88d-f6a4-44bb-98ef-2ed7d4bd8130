{php $control['form']->action .= "#frm-contactForm-form"}

{snippet form}

	{form form class: 'block-loader',
		novalidate: "novalidate",
		data-controller: "file-upload",
		data-action: "file-input:addFile->file-upload#addFile file-input:removeFile->file-upload#removeFile submit->file-upload#submit",
		data-naja-history: "off"
	}

		{control messageForForm, $flashes, $form}

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
			{input antispamHash}
		{/if}
		{*/ANTISPAM*}
		<input n:name="_do">

		<div class="u-mb-last-0 u-mb-md u-mb-3xl@lg"  data-controller="regions" data-regions-parameter-value="{$form->getParent()->lookupPath()}-countryId">
			{if isset($form['state'])}
				{include '../inp.latte', form: $form, name: state, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_state', dataAction=>'change->regions#load'}
				<a class="u-d-n" n:href="changeCountry!, countryId=>61" data-naja="" data-naja-loader=".block-loader" data-naja-history="off" data-regions-target="link" >Load regions</a>
			{/if}

				{snippet regions}
					<form n:name=form n:tag-if=false>
						{if isset($form['contact'])}
							{include '../inp.latte', form: $form, name: contact, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: 'form_label_region'}
						{/if}
					</form>
				{/snippet}


				{foreach $form->getGroups() as $groupTitle => $group}
					<h3 class="h3 u-mt-0">{_$groupTitle}</h3>
					{foreach $group->getControls() as $groupControl}
						{var $groupControlName = $groupControl->getName()}
						{var $groupControlCaption = $groupControl->getCaption()}
						{if $groupControl instanceOf \Nette\Forms\Controls\TextInput && $groupControlName !== 'antispamNoJs'}
							{include '../inp.latte', form: $form, name: $groupControlName, pClass: 'f-std__inp', labelClass: 'inp-label', labelLang: $groupControlCaption}
						{/if}
						{if $groupControl instanceOf \Nette\Forms\Controls\CheckboxList}
							<p class="f-std__inp">
								{foreach $form[$groupControlName]->getItems() as $key => $label}
									<label n:name="$groupControlName:$key" class="inp-item inp-item--checkbox inp-label">
										<input type="checkbox" n:name="$groupControlName:$key" value="{$key}" class="inp-item__inp">
										<span class="inp-item__text">
											{_$label|noescape}
										</span>
									</label>
								{/foreach}
							</p>
						{/if}
						{if $groupControl instanceOf \Nette\Forms\Controls\RadioList}
							<p class="f-std__inp">
								{foreach $form[$groupControlName]->getItems() as $key => $label}
									<label n:name="$groupControlName:$key" class="inp-item inp-item--radio inp-label">
										<input n:name="$groupControlName:$key" class="inp-item__inp">
										<span class="inp-item__text">
											{_$label|noescape}
										</span>
									</label>
								{/foreach}
							</p>
						{/if}
					{/foreach}
				{/foreach}

			{if isset($form['files'])}
				{if $type == 'career' || $forcedAllowAttachmentAll}
					{include '../inp.latte', form: $form, name: files, pClass: 'f-std__inp', labelClass: 'inp-label u-vhide', labelLang: $fileUploadText, accept=>"application/msword, application/vnd.ms-excel, application/vnd.ms-powerpoint, text/plain, application/pdf, image/*"}
				{else}
					{include '../inp.latte', form: $form, name: files, pClass: 'f-std__inp', labelClass: 'inp-label u-vhide', labelLang: $fileUploadText, accept=>"image/*"}
				{/if}
			{/if}
			{* {if isset($pages)}
				{capture $termsLink}{plink $pages->conditions}{/capture}
				{include '../inp.latte', form: $form, name: agree, pClass: 'f-std__inp', labelClass: 'f-std__label inp-label', labelLang: 'form_label_terms', labelReplace=>array('%link' => $termsLink->__toString())}
			{/if} *}
		</div>

		<p class="u-mb-0 u-mb-xxs@lg">
			<button type="submit" class="btn btn--transparent">
				<span class="btn__text">
					{_'btn_send'}
				</span>
			</button>
		</p>


		<div class="block-loader__loader"></div>
	{/form}
{/snippet}
