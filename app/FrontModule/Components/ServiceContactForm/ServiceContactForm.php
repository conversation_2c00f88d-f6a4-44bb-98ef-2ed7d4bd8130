<?php declare(strict_types=1);

namespace App\FrontModule\Components\ServiceContactForm;

use App\FrontModule\Components\ContactForm\BaseContactForm;
use App\Model\Gtm\GtmContactSubmitEvent;
use App\Model\Orm\Routable;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\Utils\HubspotFormService\HubspotFormService;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use Nette\Utils\Validators;
use Tracy\Debugger;

class ServiceContactForm extends BaseContactForm
{
	private const serviceTypes= [
		'reupholstery' => 'form_checkbox_reupholstery',
		'refinishing' => 'form_checkbox_refinishing',
		'coloring' => 'form_checkbox_coloring',
		'repair' => 'form_checkbox_repair',
		'exchange' => 'form_checkbox_exchange',
		'other' => 'form_checkbox_other',
	];

	private const delivery = [
		'own' => 'form_label_delivery_own',
		'ton' => 'form_label_delivery_ton',
	];

	private const deliveryTon = [
		'ton_delivery_packed' => 'form_label_delivery_ton_packed',
		'ton_delivery_unpacked' => 'form_label_delivery_ton_unpacked',
	];

	protected function createComponentForm(): UI\Form
	{
		$form = $this->commonFormFactory->create();
		$form->setTranslator($this->translator);

		$form->addGroup("form_label_contact_details");
		$form->addText('name', 'form_label_name_and_surname')->setRequired();
		$form->addEmail('email', 'form_label_email')
			->setRequired();
		$form->addText('phone', 'form_label_telephone')
			->setRequired();

		$form->addGroup('form_label_billing_address');
		$form->addText('billingStreet', 'form_label_street')->setRequired();
		$form->addText('billingCity', 'form_label_city')->setRequired();
		$form->addText('billingZip', 'form_label_zip')->setRequired();
		$form->addText('billingIco', 'form_label_ic');

		$form->addGroup('form_label_service_type');
		$form->addCheckboxList("serviceType", "form_label_service_type", self::serviceTypes);

		$form->addText('serviceTypeOther', '' )->setHtmlAttribute('placeholder', 'form_label_service_type_other');
		$form->addInteger('amount', 'form_label_amount_to_renovate')->setRequired();

		$form->addGroup('form_label_delivery');
		$form->addRadioList("delivery", "", self::delivery);

		$form->addRadioList("ton_delivery", "", self::deliveryTon);


		if ($this->contactFormStatus->type !== 'contact') {
			$form->addMultiUpload('files', 'form_label_photos');
		}

		$form->addSubmit('send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[] = [$this, 'formError'];
		$this->attachAntispamTo($form);

		return $form;
	}


	public function formValidate(UI\Form $form, ArrayHash $values): void
	{
		bdump($values);
		if(in_array('other',$values->serviceType, true) && empty($values->serviceTypeOther)){
			$form->addError('form_error_service_type_other');
		}

		if($values->delivery === 'ton' && $values->ton_delivery === null){
			$form->addError('form_error_delivery_ton');
		}

	}

	/**
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$values->type = $this->contactFormStatus->type;
			\assert($this->object instanceof Routable);
			$values->page = $this->object->getNameTitle();
			$values->pageLink = $this->linkFactory->linkTranslateToNette($this->object);
			$contactId = (isset($values->contact) ? (int) $values->contact : null);

			$replyTo = [
				$values->email,
				$values->name,
			];

			$serviceTypes = [];
			foreach ($values->serviceType as $serviceType) {
				$serviceType[$serviceType] = $this->translator->translate(self::serviceTypes[$serviceType]);
			}
			$values->serviceType = implode(', ', $serviceTypes);

			if($values->delivery === 'ton'){
				$values->deliveryTon = $this->translator->translate(self::deliveryTon[$values->ton_delivery]);
			}

			$values->delivery = $this->translator->translate(self::delivery[$values->delivery]);


			$recipient = $this->getCompanyRecipient($contactId);

			$this->commonEmail->send(
				from: '',
				to: $recipient,
				dbTemplate: 'service_order',
				data: (array)$values,
				replyTo: $replyTo,
				files: ($values->files ?? []),
			);

			$this->gtm->pushEvent((new GtmContactSubmitEvent($this->gtm))->setup());


			$hubSpotType = HubspotFormService::FORM_TYPE_CUSTOMIZED;
			/** @var ContactLocalization $contactLocalization */
			$contactLocalization = $this->contactFormStatus->getContactLocalizations()->getBy(['id' => $contactId]);
			$this->hubspotFormService->submitForm($hubSpotType, $values, $values->pageLink, $values->page, $this->contactFormStatus->getState(), $contactLocalization);

			$this->flashMessage($this->okMessage, 'ok');

		} catch (\Throwable $e) {
			Debugger::log($e, 'error');
			$this->flashMessage('Operation failed', 'error');
		}

		if ($this->presenter->isAjax()) {
			$form->reset();
			$this->redrawControl('form');
			$this->presenter->redrawControl('gtmEvents');
		} else {
			$this->presenter->redirect('this');
		}
	}

	public function render(array $props = []): void
	{
		try {
			$this->template->title = isset($props['title']) && $props['title'] !== false ? $props['title'] : $this->translator->translate('form_title_default');
			$this->template->content = isset($props['content']) && $props['content'] !== false ? $props['content'] : null;
			$this->template->spacing = isset($props['spacing']) && $props['spacing'] !== false ? $props['spacing'] : null;
			$this->template->fileUploadText = $this->getUploadInputLabel();

			$this->template->setTranslator($this->translator);
			$this->template->object = $this->object;
			$this->template->type = $this->contactFormStatus->type;
			$this->template->forcedAllowAttachmentAll = $this->contactFormStatus->forcedAllowAttachmentAll;
			$this->template->contactLocalization = $this->contactFormStatus->forcedContactPerson;
			$this->template->pages = $this->mutationHolder->getMutation()->pages;
//			if ($this->presenter->isAjax()) {
//				$this->presenter->redrawControl();
//				$this->redrawControl('form');
//			}

			$this->template->render(__DIR__ . '/serviceContactForm.latte');
		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}

	private function getCompanyRecipient(int $contactId = null): string
	{
		if ($this->contactFormStatus->forcedRecipientEmail !== null
			&& Validators::isEmail($this->contactFormStatus->forcedRecipientEmail)) {
			return $this->contactFormStatus->forcedRecipientEmail;
		}

		if ($contactId !== null
			&& ($selectedContactLocalization = $this->contactFormStatus->getContactLocalizations()->getById($contactId)) instanceof ContactLocalization
			&& isset($selectedContactLocalization->getParent()->cf->info->mail)
			&& Validators::isEmail($selectedContactLocalization->getParent()->cf->info->mail)
		) {
			return $selectedContactLocalization->getParent()->cf->info->mail;
		}

		if ($this->contactFormStatus->forcedContactPerson !== null
			&& isset($this->contactFormStatus->forcedContactPerson->getParent()->cf->info->mail)
			&& Validators::isEmail($this->contactFormStatus->forcedContactPerson->getParent()->cf->info->mail)
		) {
			return $this->contactFormStatus->forcedContactPerson->getParent()->cf->info->mail;
		}

		if ($this->contactFormStatus->type === 'career') {
			return $this->mutationHolder->getMutation()->hrEmail;
		}

		return $this->mutationHolder->getMutation()->contactEmail;
	}
}
