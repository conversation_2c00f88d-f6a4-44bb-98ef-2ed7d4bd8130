{if count($breadcrumbs) > 0}
	<script type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@type": "BreadcrumbList",
		"itemListElement": [
		{foreach $breadcrumbs as $key=>$i}

			{if $i instanceOf App\Model\Orm\Routable}
				{var $url = $mutation->getBaseUrl() . $presenter->link($i)}
			{else}
				{var $url = $mutation->getBaseUrlWithPrefix()}
			{/if}

			{
				"@type": "ListItem",
				"position": {$key+1},
				"name": {$i->nameAnchor},
				"item": {$url}
			}{if !$iterator->last},{/if}
		{/foreach}
		]
	}
	</script>
{/if}