{default $class = false}
{default $person = false}
{default $showBtn = false}

<div n:if="$person" class="b-representative">
	{php $img = isset($person->contact->cf->info->image) ? $person->contact->cf->info->image->getEntity() ?? false : false}
	<div n:if="$img" class="b-representative__img img img--3-2">
		<img src="{$img->getSize('md')->src}" alt="" loading="lazy">
	</div>
	<div class="b-representative__content">
		<h2 class="h1 u-mb-xxs">
			{$person->name}
		</h2>

		{var $position = $person->getPosition()}
		<p n:if="$position" class="decor u-mb-lg@md">
			{$position->value}
		</p>
		<p n:ifcontent class="u-mb-xs">
			<a n:if="$person->contact->cf?->info?->phone ?? false" href="tel:{$person->contact->cf->info->phone|replace:' ',''}" class="u-d-b">
				{$person->contact->cf->info->phone}
			</a>
			<a n:if="$person->contact->cf?->info?->mail ?? false" href="mailto:{$person->contact->cf->info->mail|replace:' ',''}" class="u-d-b">
				{$person->contact->cf->info->mail}
			</a>
		</p>
		<p n:if="$showBtn" class="u-mb-0">
			<a href="#contact" class="btn btn--secondary btn--transparent">
				<span class="btn__text">
					<span class="item-icon item-icon--after">
						{('arrow-down')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"btn_leave_message"}
						</span>
					</span>
				</span>
			</a>
		</p>
	</div>
</div>
