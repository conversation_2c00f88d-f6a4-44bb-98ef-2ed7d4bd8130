{default $class = 'u-mb-sm'} 
{default $toggle = $object->cf?->base_hp?->toggle ?? false}
{default $image = $object->cf?->base_hp?->image ?? false}
{default $video = $object->cf?->base_hp?->video ?? false}

<div n:if="$image" n:class="b-hero, $class">
	<div class="b-hero__img-holder">
		{if $toggle == 'video' && $video} 
			<div class="b-hero__img b-hero__img--video img">
				{include $templates.'/part/core/video.latte', class=>'b-hero__video', poster=>$image ? $image->getSize('xl')->src : false, link=>$video, autoplay=>true, loop=>true, controls=>false, ratio=>false}
			</div>
		{elseif $toggle == 'image' && $image}
			<div class="b-hero__img img">
				{php $imgLg = $image->getSize('lg')}
				{php $imgXl = $image->getSize('xl')}
				{php $imgMax = $image->getSize('max')}

				<img src="{$imgXl->src}"
					srcset="
						{$imgLg->src} 1060w,
						{$imgXl->src} 1420w,
						{$imgMax->src} 3840w"
					sizes="100vw"
					alt="{$image->name}" fetchpriority="high">
			</div>
		{/if}
		<img class="b-hero__logo" src="/static/img/logo-outlined.svg" alt="">
	</div>
	<div class="row-main">
		<h1 n:class="b-hero__title, !$object->annotation ? u-vhide">
			{if $object->annotation} 
				{$object->annotation}
			{else} 
				{_"title"}
			{/if}
		</h1>
		<p class="b-hero__more">
			<a href="#main" class="btn btn--secondary">
				<span class="btn__text">
					{_"btn_learn_more"}
				</span>
			</a>
		</p>
	</div>
</div>