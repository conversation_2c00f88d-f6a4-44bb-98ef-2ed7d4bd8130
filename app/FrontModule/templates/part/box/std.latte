{default $class = false}
{default $titleTag = 'h2'}
{default $showAnnot = false}
{default $cf = $item->cf->base ?? false}
{default $sizes = ['100vw', '50vw', '50vw', '69.6rem']}
{default $ratio = '5-3'}
{default $wide = false}
{default $inB2b = false}
{default $inRts = false}
{default $lazyLoading = true}
{default $crossroadImageField = 'crossroad_image'}
{default $crossroadImageWideField = 'crossroad_image_wide'}
{default $crossroadImageFullField = 'crossroad_image_full'}

{if $inRts}
	{var $crossroadImageField = 'crossroad_rts_image'}
	{var $crossroadImageWideField = 'crossroad_rts_image_wide'}
	{default $cf = $item->cf->base ?? false}
	{bd($cf)}
{/if}

<article n:class="b-std, $class, u-mb-last-0, link-mask">
	<p n:class="b-std__img, img, 'img--' . $ratio">
		{php $image = isset($cf->image) ? $cf->image->getEntity() ?? null : null}
		{if $wide && $cf}
			{php $crossroadImage = isset($cf->$crossroadImageWideField) ? $cf->$crossroadImageWideField->getEntity() ?? null : null}
		{elseif $cf}
			{php $crossroadImage = isset($cf->$crossroadImageField) ? $cf->$crossroadImageField->getEntity() ?? null : null}
		{else}
			{php $crossroadImage = isset($item->cf->base) && isset($item->cf->base->image) ? $item->cf->base->image->getEntity() ?? null : null}
		{/if}

		{php $img = $image ? $image : $crossroadImage}

		{php $isFullWidth = isset($item->cf->base_category->extra_category) ? $item->cf->base_category->extra_category->isFullWidth ?? false : false}

		{php $isFullWidth = $isFullWidth && isset($cf->$crossroadImageField) && isset($cf->$crossroadImageWideField) && isset($cf->$crossroadImageFullField)}


		{if $isFullWidth}
			{var $imgMobile = $cf->$crossroadImageField??->getSize('sm-'. $ratio)}
			{var $imgTablet = $cf->$crossroadImageWideField??->getSize('lg-'. $ratio)}
			{var $imgDesktop = $cf->$crossroadImageFullField??->getSize('2xl-'. $ratio)}
			<picture class="b-std__extra-img">
				<source media="(max-width:23.4375rem)" srcset="{$imgMobile->src}">
				<source media="(max-width:46.875rem)" srcset="{$imgTablet->src}">
				<img src="{$imgDesktop->src}" alt="{$img->getAlt($mutation)}"{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if}>
			</picture>
		{elseif $img}
			{var $imgSm = $img->getSize('sm-'. $ratio)}
			{var $imgMd = $img->getSize('md-' . $ratio)}
			{var $imgLg = $img->getSize('lg-' . $ratio)}
			{var $imgXl = $img->getSize('xl-' . $ratio)}
			{var $img2Xl = $img->getSize('2xl-' . $ratio)}

			<img src="{$imgXl->src}"
				 srcset="
					{$imgSm->src} 460w,
					{$imgMd->src} 700w,
					{$imgLg->src} 1060w,
					{$imgXl->src} 1420w,
					{$img2Xl->src} 2130w"
				 sizes="
				 	(max-width: 46.875rem) {$sizes[0]},
				 	(max-width: 62.5rem) {$sizes[1]},
				 	(max-width: 91.5rem) {$sizes[2]},
					{$sizes[3]}"
				 alt="{$img->getAlt($mutation)}"{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if}>
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</p>
	{if $inRts}
	{var $filter = []}
	{php $filter['flagValues']['path'][$item->id] = $item->id}
	{capture $link}{link $pages->rts, inB2b => $inB2b, filter=>$filter}{/capture}
	{else}
	{capture $link}{plink $item, inB2b => $inB2b}{/capture}
	{/if}
	<div class="b-std__wrapper u-mb-last-0">
		<h2 n:tag="$titleTag" class="b-std__title h1">

			<a href="{$link}" class="b-std__link link-mask__link">
				{$item->nameAnchor}
			</a>
		</h2>

		{if isset($item->annotation) && $showAnnot}
			<p class="b-std__desc">
				{$item->annotation|texy|noescape}
			</p>
		{/if}
	</div>
</article>
