{default $items = $object->mutation->cf?->social ?? []}
<nav n:if="count($items)" class="b-social">
	<div class="row-main">
		<h2 class="b-social__title h3">
			{_"social_title"}
		</h2>
		<ul class="b-social__list">
			{foreach $items as $item}
				<li n:if="isset($item->link) && $item->link && isset($item->title) && $item->title" class="b-social__item">
					<a href="{$item->link}" title="{$item->title}" class="btn btn--secondary@md" target="_blank">
						<span class="btn__text">
							{$item->title}
						</span>
					</a>
				</li>
			{/foreach}
			<li n:if="isset($pages->newsletterForm) && $pages->newsletterForm" class="b-social__item">
				<a n:href="$pages->newsletterForm"  title="{if isset($pages->newsletterForm->name)}{$pages->newsletterForm->name}{/if}" class="btn btn--secondary@md" data-modal='{"medium": "fetch"}' data-modal-newsletter data-snippetid="snippet--content">
					<span class="btn__text">
						{_social_newsletter}
					</span>
				</a>
			</li>
		</ul>
	</div>
</nav>
