{default $class = false}
{default $title = false}
{default $chairs = []}
{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : 'md'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '3xl'}

<div n:class="b-chairs, $class, $spacingBottom ? 'u-mb-' . $spacingBottom, $spacingBottomLg ? 'u-mb-' . $spacingBottomLg . '@md'" data-controller="masonry">
	<h2 class="h0 u-maw-7-12 u-mb-sm u-mb-md@md" n:if="$title">{$title}</h2>

	<div class="grid grid--space-between" data-masonry-target="container" n:if="count($chairs) > 0">
		{foreach $chairs as $item}
			{php $linkGroup = $item->link ?? false}
			{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
			{php $link = false}
			{php $product = false}
			{php $page = false}

			{if $linkGroup??->product ?? false}
				{php $product = $linkGroup->product->getEntity() ?? false}
				{capture $link}{plink $product}{/capture}
				{php $link = $link->__toString()}
			{elseif $linkGroup??->page ?? false}
				{php $page = $linkGroup->page->getEntity() ?? false}
				{capture $link}{plink $page}{/capture}
				{php $link = $link->__toString()}
			{elseif $linkGroup??->external ?? false}
				{php $link = $linkGroup->external}
			{/if}

			<div class="grid__cell size--6-12@md">
				<div class="b-chairs__inner u-maw-5-12">
					<div class="b-chairs__item link-mask">
						<p n:class="b-chairs__img, img, $image ? 'img--3-2' : 'img--3-4'">
							{if $image}
								<img src="{$image->getSize('md-3-2')->src}" loading="lazy" alt=""/>
							{else}
								<img src="/static/img/illust/noimg.svg" loading="lazy" alt=""/>
							{/if}
						</p>
						<p class="b-chairs__decor decor" n:if="$item->year ?? false">{$item->year}</p>
						<h3 class="b-chairs__name h1" n:if="$item->title ?? false"><span class="as-link">{$item->title}</span></h3>
						<p class="b-chairs__annot" n:if="$item->annot ?? false">{$item->annot}</p>
						<p n:if="$link" class="u-mb-0">
							<a href="{$link}" class="btn btn--secondary link-mask__link">
								<span class="btn__text">
									<span class="item-icon">
										{('arrow-right')|icon, 'item-icon__icon'}
										<span class="item-icon__text">
											{if $product}
												{_"btn_about_chairs"|replace:'%name',$product->nameAnchor}
											{else}
												{_"btn_learn_more"}
											{/if}
										</span>
									</span>
								</span>
							</a>
						</p>
					</div>
				</div>
			</div>
		{/foreach}

	</div>
</div>
