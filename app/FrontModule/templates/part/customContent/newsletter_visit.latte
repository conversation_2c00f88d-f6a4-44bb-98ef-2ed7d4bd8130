{var $spacing = isset($customContentItem->spacing) ? $customContentItem->spacing : false}
{var $title = isset($customContentItem->title) ? $customContentItem->title : false}
{var $titleVisit = isset($customContentItem->title_visit) ? $customContentItem->title_visit : false}
{var $page = isset($customContentItem->page) ? $customContentItem->page : false}
{var $image = isset($customContentItem->image) && $customContentItem->image instanceof App\Model\CustomField\LazyValue ? $customContentItem->image->getEntity() : false}
{var $btnText = isset($customContentItem->btnText) ? $customContentItem->btnText : false}

{include $templates.'/part/box/newsletter_visit.latte', title=>$title, titleVisit=>$titleVisit, page=>$page, image=>$image, btnText=>$btnText, spacing=>$spacing}