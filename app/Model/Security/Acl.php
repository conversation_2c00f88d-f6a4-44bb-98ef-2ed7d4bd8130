<?php

declare(strict_types=1);

namespace App\Model\Security;

use App\Model\ConfigService;
use Nette;
use Nette\Security\Permission;

final class Acl extends Nette\Security\Permission
{
	// role
	const ROLE_GUEST = 'guest';
	const ROLE_USER = 'user';
	const ROLE_ADMIN = 'admin';
	const ROLE_DEVELOPER = 'developer';

	// zdroje
	const RES_ADMIN = 'superadmin';
	const RES_SIGN = 'Admin:Sign';

	private ConfigService $configService;
	private array $userGroup;

	public function __construct(ConfigService $configService)
	{
		$this->configService = $configService;
		$this->userGroup = $this->configService->get('userGroup');

		$this->setRoles();
		$this->setResources();
		$this->setRules();
	}

	/**
	 * definice rolí
	 */
	private function setRoles(): void
	{
		$this->addRole(self::ROLE_GUEST);
		$this->addRole(self::ROLE_USER, self::ROLE_GUEST);
		$this->addRole(self::ROLE_ADMIN, self::ROLE_USER);
		$this->addRole(self::ROLE_DEVELOPER); // developer nic nededi, ten muze vse
	}

	/**
	 * definice zdrojů
	 */
	private function setResources(): void
	{
		// module
		$this->addResource(self::RES_ADMIN);
		$this->addResource(self::RES_SIGN);

		// moduly
		foreach ($this->configService->get('modules') as $module => $title) {
			$this->addResource($module);
		}
	}

	/**
	 * definice pravidel
	 */
	private function setRules(): void
	{
		foreach ($this->userGroup as $role => $group) {
			// nastavení zdrojů a akcí
			if ($group['setPermission'] && isset($group['sources']) && is_array($group['sources'])) {
				if ($group['sources'] === []) { // prázdný seznam zdrojů == právo na všechny zdroje
					$this->allow($role, Permission::ALL, Permission::ALL);
				} else {
					foreach ($group['sources'] as $source => $actions) {
						$this->allow($role, $source, $actions);
					}
				}

				if ($group['denySources'] !== []) { // zakázané zdroje
					foreach ($group['denySources'] as $source => $actions) {
						$this->deny($role, $source, $actions);
					}
				}
			}
		}

		$this->allow(self::ROLE_DEVELOPER); // developer může všechno
	}
}
