<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\Product\Product;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceRepository;

class StoreData implements Convertor
{
	public function __construct(
		private readonly ParameterRepository $parameterRepository,
		private readonly ProductInstanceRepository $productInstanceRepository,
	)
	{
	}



	public function convert(Product $product): array
	{
		$data = [];
		$availabilityParameter = $this->parameterRepository->getByChecked([
			'uid' => Parameter::UID_AVAILABILITY,
		]);
		assert($availabilityParameter instanceof Parameter);
		$productInstances = $this->productInstanceRepository->findBy([
			'variant->product' => $product,
		]);

		if ($productInstances->count() > 0) {
			$availabilityValue = $availabilityParameter->options->toCollection()->getByChecked([
				'internalAlias' => 'in-stock',
			]);
		} else {
			$availabilityValue = $availabilityParameter->options->toCollection()->getByChecked([
				'internalAlias' => 'on-request',
			]);
		}
		$data[Parameter::UID_AVAILABILITY] = [$availabilityValue->id];
		$data['isInStore'] = (bool) $product->totalSupplyCount;
		return $data;
	}

}
