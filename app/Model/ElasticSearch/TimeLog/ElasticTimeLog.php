<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\TimeLog;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Entity;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Mutation\Mutation;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @param Convertor[] $convertors
 */
class ElasticTimeLog implements Entity
{

	public function __construct(
		private readonly string $type,
		private readonly array $data,
	)
	{
	}

	public function getId(): string
	{
		return (string) null;
	}

	public function getData(Mutation $mutation): array
	{
		return [
			'type' => $this->type,
			'createdAt' => ConvertorHelper::convertTime(new DateTimeImmutable()),
			'data' => $this->data,
		];
	}

}
