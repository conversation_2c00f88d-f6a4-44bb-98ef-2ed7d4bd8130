<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\PostType\Career\Model\Orm\CareerLocalization;
use App\PostType\Collection\Model\Orm\CollectionLocalization;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Press\Model\Orm\PressLocalization;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Service\Model\Orm\ServiceLocalization;
use App\PostType\Showroom\Model\Orm\ShowroomLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticAll implements Entity
{

	public const TYPE_TREE = 'tree';
	public const TYPE_BLOG = 'blogLocalization';
	public const TYPE_BLOG_TAG = 'blogTagLocalization';
	public const TYPE_AUTHOR = 'authorLocalization';
	public const TYPE_PRODUCT = 'product';
	public const TYPE_SEOLINK = 'seoLinkLocalization';
	public const TYPE_PRESS = 'pressLocalization';
	public const TYPE_SHOWROOM = 'showroomLocalization';
	public const TYPE_CONTACT = 'contactLocalization';
	public const TYPE_CAREER = 'careerLocalization';
	public const TYPE_REFERENCE = 'referenceLocalization';
	public const TYPE_SERVICE = 'serviceLocalization';
	public const TYPE_PRODUCT_INSTANCE = 'product_instance';
	public const TYPE_COLLECTION = 'collection';

	public function __construct(
		private object $object,
		private array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$class = get_class($this->object);

		if (isset($this->object->id)) {
			return match ($class) {
				BlogLocalization::class => self::TYPE_BLOG . '-' . $this->object->id,
				BlogTagLocalization::class => self::TYPE_BLOG_TAG . '-' . $this->object->id,
				AuthorLocalization::class => self::TYPE_AUTHOR . '-' . $this->object->id,
				CommonTree::class, CatalogTree::class => self::TYPE_TREE . '-' . $this->object->id,
				Product::class => self::TYPE_PRODUCT . '-' . $this->object->id,
				SeoLinkLocalization::class => self::TYPE_SEOLINK . '-' . $this->object->id,
				PressLocalization::class => self::TYPE_PRESS . '-' . $this->object->id,
				ShowroomLocalization::class => self::TYPE_SHOWROOM . '-' . $this->object->id,
				ContactLocalization::class => self::TYPE_CONTACT . '-' . $this->object->id,
				CareerLocalization::class => self::TYPE_CAREER . '-' . $this->object->id,
				ReferenceLocalization::class => self::TYPE_REFERENCE . '-' . $this->object->id,
				ServiceLocalization::class => self::TYPE_SERVICE . '-' . $this->object->id,
				ProductInstanceLocalization::class => self::TYPE_PRODUCT_INSTANCE . '-' . $this->object->id,
				CollectionLocalization::class => self::TYPE_COLLECTION . '-' . $this->object->id,
				default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
			};
		} else {
			throw new LogicException('Missing primary key for entity');
		}
	}

	public function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}

		return array_merge(...$convertedData);
	}

}
