<?php declare(strict_types = 1);

namespace App\Model\Mailer;

use App\Model\ConfigService;
use Nette\Http\FileUpload;
use Nette\Mail\Mailer;
use Nette\Mail\Message;
use Nette\Mail\SendmailMailer;
use Nette\Utils\Strings;
use Pelago\Emogrifier\CssInliner;
use stdClass;
use App\Model\Mutation\MutationHolder;
use Symfony\Component\CssSelector\Exception\ParseException;

final class BaseMailer
{

	public function __construct(
		private readonly MutationHolder $mutationHolder,
		private readonly ConfigService $configService,
		private readonly Mailer $mailer,
	)
	{
	}

	/**
	 * @param string|array|null $from
	 * @param string|array|null $to
	 * @throws ParseException
	 */
	public function send($from, $to, string $subject, string $html, ?stdClass $data = null, array $replyTo = [], array $files = []): void
	{
		$css = file_get_contents(FE_TEMPLATE_DIR . '/email/newsletter.css');
//		$emogrifier = new \Pelago\Emogrifier($html, $css);
//		$html = $emogrifier->emogrify();
		$html = CssInliner::fromHtml($html);
		if (is_string($css)) {
			$html->inlineCss($css);
		}

		$html = $html->render();

		if ($this->configService->get('emailsEchoDie') === true) {
			echo $html;
			die;
		}

		if (in_array($from, [null, [], ''], true)) {
			$from = $this->mutationHolder->getMutation()->getRealFromEmail();
		}

		if (in_array($to, [null, [], ''], true)) {
			$to = $this->mutationHolder->getMutation()->getRealFromEmail();
		}

		$tmpFrom = $from;
		if (is_array($from) && isset($from[0])) {
			$from = $from[0];
		}

		if (is_array($tmpFrom) && isset($tmpFrom[1])) {
			$fromName = $tmpFrom[1];
		} else {
			$fromName = null;
		}

		$mail = new Message();

		if (isset($data->attachments)) {
			foreach ($data->attachments as $attachment) {
				$mail->addAttachment($attachment);
			}
		}
        foreach ($files as $file) {
            if ($file instanceof FileUpload) {
                if ($file->isOk()) {
                    $mail->addAttachment($file->getSanitizedName(), $file->getContents(), $file->getContentType());
                }
            }

        }

		if ($replyTo !== []) {
			$replyEmail = $replyTo[0];
			$replyName = (isset($replyTo[1])) ? $replyTo[1] : null;
			$mail->addReplyTo($replyEmail, $replyName);
		}

		$mail->setFrom($from, $fromName)
			->setSubject($subject)
			->setHtmlBody($html, WWW_DIR . '/static/img');

		$forceRecipient = (string) $this->configService->getParam('forceRecipient');
		if (Strings::length($forceRecipient) > 0) {
			$to = $forceRecipient;
		}

		if (is_array($to)) {
			foreach ($to as $t) {
				$t = trim($t);
				$mail->addTo($t);
			}
		} else {
			$mail->addTo($to);
		}

		$this->mailer->send($mail);
	}

}
