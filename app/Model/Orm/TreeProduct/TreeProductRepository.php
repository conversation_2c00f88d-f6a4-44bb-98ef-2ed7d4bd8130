<?php declare(strict_types = 1);

namespace App\Model\Orm\TreeProduct;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\IMapper;
use Nextras\Orm\Repository\IDependencyProvider;
use Nextras\Orm\Repository\Repository;

/**
 * @method TreeProduct getById($id)
 */
final class TreeProductRepository extends Repository
{

	public function __construct(
		IMapper $mapper,
		IDependencyProvider|null $dependencyProvider,
		private readonly ProductRepository $productRepository,
		private readonly TreeRepository $treeRepository,
	)
	{
		parent::__construct($mapper, $dependencyProvider);
	}

	public static function getEntityClassNames(): array
	{
		return [TreeProduct::class];
	}

	public function findForProduct(Product $product, string $type): ICollection
	{
		return $this->treeRepository->findBy([
			'treeProducts->type' => $type,
			'treeProducts->product' => $product,
		]);
	}


	public function findForTree(Tree $tree, string $type): ICollection
	{
		return $this->productRepository->findBy([
			'treeProducts->type' => $type,
			'treeProducts->tree' => $tree,
		]);
	}


	public function replace(Product $product, Tree $tree, string $type, int $sort): TreeProduct
	{
		if (!$treeProduct = $this->getByReferenceAndType($product, $tree, $type)) {
			$treeProduct = $this->createNew();
		}

		return $this->update($treeProduct, $product, $tree, $sort, $type);
	}


	public function getByReferenceAndType(Product $product, Tree $tree, string $type): ?TreeProduct
	{
		return $this->getBy([
			'type' => $type,
			'tree' => $tree,
			'product' => $product,
		]);
	}


	public function createNew(): TreeProduct
	{
		$newTreeProduct = new TreeProduct();
		$this->attach($newTreeProduct);

		return $newTreeProduct;
	}

	public function update(TreeProduct $treeProduct, Product $product, Tree $tree, int $sort, string $type): TreeProduct
	{
		$treeProduct->type = $type;
		$treeProduct->product = $product;
		$treeProduct->tree = $tree;
		$treeProduct->sort = $sort;
		return $treeProduct;
	}

}
