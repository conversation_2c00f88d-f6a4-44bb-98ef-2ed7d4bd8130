<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductGroup;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\DocumentProduct\DocumentProduct;
use App\Model\Orm\ProductVariant\ProductVariant;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property int $extId
 *
 *
 * RELATIONS
 * @property ProductVariant|null $productVariant {m:1 ProductVariant::$productGroups}
 *
 * VIRTUALS
 */
final class ProductGroup extends BaseEntity
{

}
