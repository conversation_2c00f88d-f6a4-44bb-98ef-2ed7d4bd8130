<?php declare(strict_types = 1);

namespace App\Model\Orm\ConfiguratorTranslation;

use App\Model\Orm\ConfiguratorTranslationExtender\ConfiguratorTranslationExtender;
use League\Csv\Reader;
use League\Csv\Writer;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use RuntimeException;

class ConfiguratorTranslationModel
{

	public const CSV_HEADER = ['id', 'uid', 'cs', 'sk', 'en', 'pl', 'de', 'fr', 'it', 'es'];

	public function __construct(
		private readonly ConfiguratorTranslationRepository $configuratorTranslationRepository,
	)
	{
	}


	public function exportAll(bool $windows = false): string
	{
		$file = TEMP_DIR . '/configurator-translations-' . Random::generate() . '.csv';
		if (file_exists($file)) {
			unlink($file);
		}

		$writer = Writer::createFromPath($file, 'w+');
		if ($windows) {
			$writer->setEnclosure('"');
			$writer->setDelimiter(';');
		} else {
			$writer->setEnclosure('"');
			$writer->setDelimiter(';');
		}

		$writer->insertOne(self::CSV_HEADER);
		foreach ($this->configuratorTranslationRepository->findRows() as $row) {
			$writer->insertOne([$row->id, $row->uid, $this->convert($row->name, $windows), $this->convert($row->cs, $windows), $this->convert($row->en, $windows), $this->convert($row->sk, $windows)]);
		}

		return $file;
	}

	private function convert(string $string, bool $windows): string
	{
		if (!$windows) {
			return $string;
		}
		if (($convertedString = iconv('UTF-8', 'Windows-1250', $string)) === false) {
			return $string;
		}

		return $convertedString;
	}

	public function import(string $temporaryFile): array
	{
		$report = ['total' => ['updated' => 0, 'skipped' => 0]];
		$reader = Reader::createFromPath($temporaryFile);
		$reader->setHeaderOffset(0);
//		CharsetConverter::addTo($reader, 'cp-1250','UTF-8');
		$isUTF = Strings::checkEncoding((string)file_get_contents($temporaryFile));

		$reader->setDelimiter(';');
		//check header
		$header = $reader->getHeader();
		if ($header !== self::CSV_HEADER) {
			throw new RuntimeException('Bad csv format, expected ' . implode(', ', self::CSV_HEADER));
		}

		//import rows
		foreach ($reader->getRecords($header) as $row) {
			$configuratorTranslation = $this->configuratorTranslationRepository->getById($row['id']);
			if ($configuratorTranslation !== null) {
				$configuratorTranslation->cs = $this->importConvert($row['cs'], $isUTF);
				$configuratorTranslation->en = $this->importConvert($row['en'], $isUTF);
				$configuratorTranslation->sk = $this->importConvert($row['sk'], $isUTF);
				$configuratorTranslation->fr = $this->importConvert($row['fr'], $isUTF);
				$configuratorTranslation->de = $this->importConvert($row['de'], $isUTF);
				$configuratorTranslation->pl = $this->importConvert($row['pl'], $isUTF);
				$configuratorTranslation->it = $this->importConvert($row['it'], $isUTF);
				$configuratorTranslation->es = $this->importConvert($row['es'], $isUTF);
				$report['total']['updated']++;
				$this->configuratorTranslationRepository->persist($configuratorTranslation);
			} else {
				$report['total']['skipped']++;
			}
		}

		$this->configuratorTranslationRepository->flush();
		return $report;
	}

	private function importConvert(string $string, bool $isUTF): string
	{
		return ($isUTF || ($convertedString = iconv('Windows-1250', 'UTF-8', $string)) === false) ? $string : $convertedString;
	}

    public function importExternalParameters(ConfiguratorTranslationExtender $configuratorTranslationExtender): void
    {
		if (isset($configuratorTranslationExtender->cf->base->parameters)
			&& $parameters = $configuratorTranslationExtender->cf->base->parameters) {
			foreach ($parameters as $parameterData) {
				if (isset($parameterData->parameter)) {
					$parameterName = trim($parameterData->parameter);
					$parameterUid = self::sanitize($parameterName);
					$newConfiguratorTranslationParameter = $this->configuratorTranslationRepository->getByUidOrCreateShell($parameterUid, ConfiguratorTranslation::NAMESPACE_EXTENDER);
					$newConfiguratorTranslationParameter->type = ConfiguratorTranslation::TYPE_PARAMETER;
					$newConfiguratorTranslationParameter->code = $newConfiguratorTranslationParameter->uid;
					$newConfiguratorTranslationParameter->parameterName = $parameterName;
					$newConfiguratorTranslationParameter->parameterCode = $newConfiguratorTranslationParameter->uid;
					$newConfiguratorTranslationParameter->name = $parameterName;
					if (!$newConfiguratorTranslationParameter->isPersisted()) {
						$newConfiguratorTranslationParameter->cs = $newConfiguratorTranslationParameter->name;
					}
					$newConfiguratorTranslationParameter->editedAt = 'now';

					$this->configuratorTranslationRepository->persist($newConfiguratorTranslationParameter);

					if (isset($parameterData->values)) {
						foreach ($parameterData->values as $valueData) {
							$parameterValueUid = self::sanitizeValue($parameterUid, $valueData->value);
							$newConfiguratorTranslationValue = $this->configuratorTranslationRepository->getByUidOrCreateShell($parameterValueUid, ConfiguratorTranslation::NAMESPACE_EXTENDER);

							$newConfiguratorTranslationValue->type = ConfiguratorTranslation::TYPE_VALUE;
							$newConfiguratorTranslationValue->code = $newConfiguratorTranslationValue->uid;
							$newConfiguratorTranslationValue->parameterName = $newConfiguratorTranslationParameter->parameterName;
							$newConfiguratorTranslationValue->parameterCode = $newConfiguratorTranslationParameter->uid;
							$newConfiguratorTranslationValue->name = trim($valueData->value);
							if (!$newConfiguratorTranslationValue->isPersisted()) {
								$newConfiguratorTranslationValue->cs = $newConfiguratorTranslationValue->name;
							}
							$newConfiguratorTranslationValue->editedAt = 'now';

							$this->configuratorTranslationRepository->persist($newConfiguratorTranslationValue);
						}
					}
				}
			}
		}
    }

	public static function sanitize(string $parameterName): string
	{
		return Strings::webalize(trim($parameterName));
	}

	public static function sanitizeValue(string $parameterUid, string $value): string
	{
		return $parameterUid . '#' . Strings::webalize(trim($value));
	}
}
