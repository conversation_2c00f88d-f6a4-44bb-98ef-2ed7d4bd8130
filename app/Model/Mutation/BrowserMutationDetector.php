<?php declare(strict_types = 1);

namespace App\Model\Mutation;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\PostType\Core\Model\LocalizationEntity;
use Closure;
use DateInterval;
use DateTimeImmutable;
use Nette\Http\Request;
use Nette\Http\Response;

class BrowserMutationDetector
{

	public const COOKIE_KEY = 'language_iso_prefix_detected';

	public function __construct(
		private MutationsHolder $mutationsHolder,
		private Request $request,
		private Response $response,
	)
	{
	}

	public function detect(): ?Mutation
	{
		$futureMutation = $this->getMutationFromCookie();

		if ($futureMutation === null) {
			$futureMutation = $this->getMutationFromBrowser();
		}

		return $futureMutation;
	}


	private function getMutationFromBrowser(): ?Mutation
	{
		$acceptMutationsIsoCodes = $this->mutationsHolder->getMutationByLangIsoCode();

		$acceptIsoCodes = array_map(function (Mutation $mutation) {
			return $mutation->isoCodePrefix;
		}, $acceptMutationsIsoCodes);

		$lang = $this->request->detectLanguage($acceptIsoCodes);
		$acceptMutations = $this->mutationsHolder->findAll();

		return (isset($acceptMutations[$lang])) ? $acceptMutations[$lang] : null;
	}


	public function setCookie(Mutation $futureMutation): void
	{
		$now = new DateTimeImmutable();
		$nowPlus1Year = $now->add(DateInterval::createFromDateString('1 year'));
		$this->response->setCookie(self::COOKIE_KEY, $futureMutation->langCode, $nowPlus1Year);
	}

	private function getMutationFromCookie(): ?Mutation
	{
		$langCode = $this->request->getCookie(self::COOKIE_KEY);

		if ($langCode === null) {
			return null;
		}

		$acceptMutations = $this->mutationsHolder->findAll();
		return (isset($acceptMutations[$langCode])) ? $acceptMutations[$langCode] : null;
	}


	/**
	 * @phpstan-param Closure(LocalizationEntity $routable, Mutation $detectedMutation): void $afterDetection
	 */
	public function detectAndTryRedirect(Routable $routable, Closure $afterDetection): void
	{
		if ($routable instanceof LocalizationEntity) {
			$detectedMutationId = $this->request->getCookie(self::COOKIE_KEY);
			if ($detectedMutationId === null || $routable->getMutation()->id !== $detectedMutationId) {
				$detectedMutation = $this->detect();
				if ($detectedMutation !== null) {
					$this->setCookie($detectedMutation);
					($afterDetection)($routable, $detectedMutation);
				}
			}
		}
	}

}
