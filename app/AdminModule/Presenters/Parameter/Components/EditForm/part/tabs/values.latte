{var $gridColumns = 4}
<div class="box-param-type" data-type='["select", "multiselect", "number"]'>
	<h2>{_'parameters_title_value'}</h2>
	<div class="crossroad-attached">
		{* vyjimka: razeni hodnot oceneni podle abecedy *}
		<div class="holder {if !$parameter->options->count()}hide{/if}">
			<div class="hd">
				<div class="grid-row">
					<p class="grid-1-{$gridColumns}">
						{_'parameters_label_value_name_internal'}
					</p>
					<p class="grid-1-{$gridColumns}">
						{_'parameters_label_value_alias_internal'}
					</p>


				</div>
			</div>
			<div class="bd">
				<ul class="sortable reset" data-copy="values"
					data-pattern='{include "../form/values.latte" gridColumns => $gridColumns}'>
					{if $parameter->options->countStored()}
						{foreach $parameter->options->toCollection()->orderBy('sort') as $k=>$parameterValue}

							{if $parameter->hasTranslatedValues}
								{var $trValuesForOption = $trValues[$parameterValue->id]}
							{else}
								{var $trValuesForOption = []}
							{/if}

							{include '../form/values.latte',
								k=> $parameterValue->id,
								data => $parameterValue,
								sort => $parameterValue->sort,
								trValue => $trValuesForOption,
								parameterValue => $parameterValue,
								gridColumns => $gridColumns,
							}
						{/foreach}
					{/if}
				</ul>
			</div>
		</div>
		<div class="ft">
			<p>
				<a href="#" class="btn btn-icon-before" data-copy="values">
					<span><span class="icon icon-plus"></span> {_'add_value_button'}</span>
				</a>
			</p>
		</div>
	</div>
</div>
