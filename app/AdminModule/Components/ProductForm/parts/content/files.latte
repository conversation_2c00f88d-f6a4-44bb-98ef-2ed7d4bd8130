
{var $props = [
	title: 'Soubory',
	id: 'files',
	variant: 'main',
	icon: $templates.'/part/icons/file-archive.svg',
	classes: ['u-mb-xxs'],
	rowMain: false,
	tags: [
		[text: 'Lokalizované'],
	]
]}




{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="grid grid--center grid--x-0 grid--y-0">
			{var $gridSize = 12}
			{if $form['productLocalizations']->components->count() >= 2}
				{var $gridSize = 6}
			{/if}

			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
				{var $mutation = $mutations->getById($mutationId)}
				{var $langCode = $mutation->langCode}

				{var $items = []}
				{foreach $localizationContainer['files']->components as $fileKey=>$fileContainer}

					{continueIf $fileKey === 'newItemMarker'}

					{var $item = [
						inps: [
							[
								input: $fileContainer['fileName'],
								placeholder: 'Zadejte název souboru'
							],
							[
								input: $fileContainer['fileId'],
								type: 'hidden',
								classes: 'u-hide',
							]
						],
						btnsAfter: [
							[
								icon: $templates.'/part/icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: [
									action: 'RemoveItem#remove'
								],
							]
						],
						data: [
							controller: 'RemoveItem File',
							removeitem-target: 'item',
							file-tags-value: '{"type": "pdf", "size": "3.45 MB"}',
						]
					]}
					{php $items[] = $item}
				{/foreach}

				{capture $localizedContent}
					{include $templates.'/part/box/list.latte',
						props: [
							data: [
								controller: 'List',
								List-name-value: 'file',
								List-mutationId-value: $mutation->id,

							],
							listData: [
								List-target: 'list',
							],
							add: true,
							file: true,
							dragdrop: true,
							items: $items
						]
					}
				{/capture}

				{if $langCode == 'en'}
					<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
						<div class="row-main">
							<div class="tag u-mb-xxs">
								{$langCode|upper}
							</div>
							{$localizedContent}
						</div>
					</div>
				{else}
					<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
						<div class="row-main">
							{include $templates.'/part/core/checkbox.latte',
								props: [
									label: '<span class="grid-inline"><span class="tag">'.strtoupper($langCode).'</span> <span>přebírá nastavení z výchozí verze</span></span>',
									input: $form['productLocalizations'][$mutation->id]['setup']['inheritFiles'],
									dataInp: [
										controller:'ToggleCheckbox',
										action:'ToggleCheckbox#changeClass',
										togglecheckbox-target-value:'#checkbox-files-'.strtolower($langCode),
										togglecheckbox-target-class-value:'is-open'
									],
								]
							}
							{var $isOpen = !($form['productLocalizations'][$mutation->id]['setup']['inheritFiles']->getValue())}

							<div id="checkbox-files-{$langCode|lower}" class="js-toggle-checkbox__content u-mt--xs {if $isOpen}is-open{/if}">
								{$localizedContent}
							</div>
						</div>
					</div>
				{/if}
			{/foreach}

		</div>
	{/block}
{/embed}
