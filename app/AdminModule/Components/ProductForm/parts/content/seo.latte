{embed $templates.'/part/box/toggle.latte', props=>[
	title: 'SEO',
	id: 'seo',
	icon: $templates.'/part/icons/google.svg',
	variant: 'main',
	classes: ['u-mb-xxs'],
	rowMain: false,
	tags: [
		[text: 'Lokalizované'],
	]
], templates=>$templates}
	{block content}
		<div class="grid grid--center grid--x-0 grid--y-0">
			{var $gridSize = 12}
			{if $form['productLocalizations']->components->count() >= 2}
				{var $gridSize = 6}
			{/if}

			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
				{var $mutation = $mutations->getById($mutationId)}
				{var $langCode = $mutation->langCode}

				<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
					<div class="row-main">
						<div class="tag u-mb-xxs">
							{$langCode}
						</div>
						{include $templates.'/part/core/inp.latte' props: [
							input: $localizationContainer['alias'],
							label: 'alias',
							classesLabel: ['title'],
							btn: 'Z názvu',
							data: [
								controller: 'Alias',
								alias-url-value: '/superadmin/product/regenerate-alias-mutation',
								alias-lang-value: $langCode,
								alias-id-value: $object->id,
								alias-name-value: $localizationContainer['name']->getValue(),
								action: 'ProductTitle:generateAlias@window->Alias#generate',
							],
							dataInp: [
								alias-target: 'inp'
							],
							dataBtn: [
								action: 'Alias#generate',
							]
						]}

						{include $templates.'/part/core/inp.latte' props: [
							input: $localizationContainer['nameAnchor'],
							label: 'nameAnchor',
							classesLabel: ['title'],
							dataInp: [
								controller: 'ProductTitleSeo',
								action: 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
								producttitleseo-lang-value: $langCode
							]
						]}

						{include $templates.'/part/core/inp.latte' props: [
							input: $localizationContainer['nameTitle'],
							label: 'nameTitle',
							classesLabel: ['title'],
							dataInp: [
								controller: 'ProductTitleSeo',
								action: 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
								producttitleseo-lang-value: $langCode
							]
						]}

						{include $templates.'/part/core/inp.latte' props: [
							input: $localizationContainer['description'],
							label: 'description',
							type: 'textarea',
							classesLabel: ['title']
						]}

						{include $templates.'/part/core/inp.latte' props: [
							input: $localizationContainer['aliasHistory'],
							label: 'aliasHistory',
							type: 'textarea',
							classesLabel: ['title']
						]}
					</div>
				</div>
			{/foreach}
		</div>

	{/block}
{/embed}




