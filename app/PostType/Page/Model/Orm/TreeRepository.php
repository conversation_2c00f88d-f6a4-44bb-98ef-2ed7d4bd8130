<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasPublicParameter;

/**
 * @method ICollection|Tree[] findLastInPath($id)
 * @method ICollection|Tree[] findInPath($id)
 * @method ICollection|Tree[] searchByName(string $q, ?int $parentId = null, ?int $pathId = null, ?array $excluded = null)
 * @method ICollection|Tree[] findFilteredPages(array $pageIds)
 *
 * @method ICollection|Tree[] findMainTreesInRelations(Tree $attachedTree, string $type)
 * @method ICollection|Tree[] findAttachedTreesInRelations(Tree $mainTree, string $type)
  *
 * @method ICollection|Tree[] addParameterValue($tree, $parameterValue)
 * @method ICollection|Tree[] removeParameterValue($tree, $parameterValue)
 * @method ICollection|Tree[] removeParameter($tree, $parameter)
 * @method ICollection|Tree[] findByParameterValueIds($ids)
 *
 * @method Tree getLanguageParent(Tree $tree)
 *
 * @method Result findAllIds(?int $limit)
 * @method Result getTreeIds($exc = false)
 *
 * @method ICollection|Tree[] findTreesInTreeProductRelations(Product $product, string $type, Mutation $mutation)
 * @method ICollection|Tree[] findTreesInProductTreeRelations(Product $product, Mutation $mutation)
 *
 * @method Tree|null getById($id)
 */
final class TreeRepository extends Repository implements CollectionById, QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [Tree::class, CatalogTree::class, CommonTree::class];
	}


	public function getEntityClassName(array $data): string
	{
		return match ($data['type']) {
			Tree::TYPE_CATALOG => CatalogTree::class,
			default => CommonTree::class,
		};
	}


	public function getByUidClear(string $uid): ?Tree
	{
		$cond = [
			'uid' => $uid,
		];
		return $this->getBy($cond);
	}


	/**
	 * @return ICollection|Tree[]
	 */
	public function findByRootId(int $rootId): ICollection
	{
		return $this->findBy([
			'rootId' => $rootId,
		]);
	}


	public function getByUid(string $uid, Mutation $mutation): ?Tree
	{
		$cond = [
			'uid' => $uid,
		];
		$cond = array_merge($cond, $this->getPublicOnlyWhere());

		return $this->findByRootId($mutation->getRealRootId())->getBy($cond);
	}


	public function getByUidAndRootId(string $uid, int $rootId): ?Tree
	{
		$cond = [
			'uid' => $uid,
		];
		$cond = array_merge($cond, $this->getPublicOnlyWhere());
		return $this->findByRootId($rootId)->getBy($cond);
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findFilteredPages($ids);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof TreeMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
