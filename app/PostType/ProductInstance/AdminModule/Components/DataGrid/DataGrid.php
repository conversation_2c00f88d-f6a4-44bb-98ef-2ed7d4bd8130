<?php declare(strict_types = 1);

namespace App\PostType\ProductInstance\AdminModule\Components\DataGrid;

use App\AdminModule\Presenters\Catalog\Components\DataGrid\DataSource\DataMapper;
use App\AdminModule\Presenters\Catalog\Components\DataGrid\DataSource\ElasticsearchDataSource;
use App\Model\ElasticSearch\Common\Convertor\ProductInstanceData;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Translator;
use Elasticsearch\ClientBuilder;
use Nette\Application\UI\Control;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Ublaboo\DataGrid\Filter\FilterSelect;
use Ublaboo\DataGrid\Utils\Sorting;

class DataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$csMutation = $this->mutationsHolder->getDefault();
		$esIndex = $this->orm->esIndex->getAllLastActive($csMutation);

		if ($esIndex === null) {
			$grid = new \Ublaboo\DataGrid\DataGrid();
			$grid->setDataSource([]);
			$grid->addColumnText('id', 'id');
			return $grid;
		}

		$client = ClientBuilder::create()->build();


		$dataSource = new ElasticsearchDataSource(
			$client, // Elasticsearch\Client
			$esIndex->esName, // Index name
			$this->getDataMappers(),
		);

		$grid = new \Ublaboo\DataGrid\DataGrid();


		$grid->setStrictSessionFilterValues(false);
		$baseFilter = new FilterSelect($grid, 'type', 'type', [
			'type' => ProductInstanceData::TYPE,
		], 'type');

		$baseFilter = $baseFilter->setValue(ProductInstanceData::TYPE);
		$dataSource->applyFilterSelect($baseFilter);

		$baseSort = new Sorting(['nameSort' => 'asc']);
		$dataSource->sort($baseSort);

		$grid->setDataSource($dataSource);
		$grid->setItemsPerPageList([50, 100, 150], false);

//		$grid->addColumnText('id', 'id')->setSortable()->setFilterText()->setExactSearch();
		$grid->addColumnText('nameSort', 'nameSort')->setSortable()->setFilterText();

		$grid->addColumnText('publish', 'ublaboo_publish', 'filter.publish')
			->setRenderer(function ($data) {
				$parts = [];
				if (isset($data['filter']['publish']) && $data['filter']['publish']) {
					foreach ($data['filter']['publish'] as $publish) {
						if (preg_match('/(.*)_public$/', $publish, $matches)) {
							$parts[] = $matches[1];
						}
					}
				}

				return implode(', ', $parts);
			})
			->setFilterMultiSelect($this->getPublishOptions())->setPrompt('Vše');

		$grid->addColumnText('codes', 'ublaboo_codes', 'filter.codes')
			->setRenderer(function ($data) {
				return (isset($data['filter']['codes']) && $data['filter']['codes']) ? implode(', ', $data['filter']['codes']) : '';
			})
			->setFilterText();

		$grid->addColumnDateTime('public', 'public', 'filter.publishDate')
			->setRenderer(function ($data) {
				$parts = [];
				if (isset($data['filter']['publishDate'][0])) {
					$parts[] = (new DateTimeImmutable())->setTimestamp($data['filter']['publishDate'][0])->format('d.m.Y');
				}

				if (isset($data['filter']['publishDate'][1])) {
					$parts[] = (new DateTimeImmutable())->setTimestamp($data['filter']['publishDate'][1])->format('d.m.Y');
				}

				return implode(' - ', $parts);
			})

			->setFilterDateRange('filter.publishDate');

		$grid->addColumnText('mutation', 'label_mutation', 'filter.mutationId')
			->setRenderer(function ($data): string {
				if (!isset($data['filter']['mutationId'])) {
					return '';
				}
				return $this->orm->mutation->getById($data['filter']['mutationId'])->name ?? '';
			})
			->setFilterSelect($this->orm->mutation->findAll()->fetchPairs('id', 'name'))
			->setPrompt($this->translator->translate('all'));


		$grid->addAction('edit', 'Edit', 'ProductInstance:edit')->setClass('btn btn-xs btn-primary');

		$grid->setDefaultSort(['nameSort' => 'asc']);

		$grid->setTranslator($this->translator);

		return $grid;
	}

//	private function getOptions(Parameter $parameter): array
//	{
//		$options = [];
//		foreach ($parameter->options as $option) {
//			$options[(string) $option->internalValue] = (string) $option->internalValue;
//		}
//
//		return $options;
//	}


	private function getPublishOptions(): array
	{
		$rows = [];
		foreach ($this->mutationsHolder->findAll(false) as $mutation) {
			$rows[sprintf('%s_public', $mutation->langCode)] = sprintf('%s - publikováno', $mutation->langCode);
			$rows[sprintf('%s_hide', $mutation->langCode)] = sprintf('%s - skryto', $mutation->langCode);
		}

		return $rows;
	}

	private function getDataMappers(): array
	{
		$mappers = [];

		// add boost
		$template['term']['type'] = ['value' => null, 'boost' => 10];
		$mappers[] = new DataMapper(['[term][type][query]' => '[term][type][value]'], $template );

		// {"range":{"filter.publishDate":{"gte":1625349600,"lte":1654379999}}}
		// split and switch
		$mappers[] = new DataMapper([
			'[range][filter.publishDate][gte]' => '[0][range][filter.publishDate][gte]',
			'[range][filter.publishDate][lte]' => '[1][range][filter.publishDate][lte]'
			] );

		// fix old 'should' syntax for ES
		$mappers[] = new DataMapper([
			'[bool][should][0]' => '[bool][should]',
		] );

		return $mappers;
	}

}
