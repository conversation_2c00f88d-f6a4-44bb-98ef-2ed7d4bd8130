<?php declare(strict_types = 1);

namespace App\PostType\Contact\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\ElasticSearch\Common\ContactRepository;
use App\PostType\Contact\FrontModule\Components\ContactList\ContactList;
use App\PostType\Contact\FrontModule\Components\ContactList\ContactListFactory;
use App\PostType\Contact\FrontModule\Components\ServicePages\ServicePages;
use App\PostType\Contact\FrontModule\Components\ServicePages\ServicePagesFactory;
use App\PostType\Contact\Model\Orm\Contact;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\PostType\Page\Model\Orm\CommonTree;

/**
 * @method ContactLocalization getObject()
 */
final class ContactPresenter extends BasePresenter
{

	use HasCustomContent<PERSON>enderer;

//	private ContactLocalization $contactLocalization;


	public function __construct(
		private readonly ContactListFactory $contactListFactory,
	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function actionB2bContact(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function actionSalesRepresentatives(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function actionPress(CommonTree $object): void
	{
		$this->setObject($object);
	}


	public function actionDetail(ContactLocalization $object): void
	{
		$this->redirect($this->mutation->pages->title);
//		$this->setObject($object);
//		$this->contactLocalization = $object;
	}

	public function renderDetail(): void
	{
//		$this->template->contactLocalization = $this->contactLocalization;
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}


	public function createComponentContactList(): ContactList
	{
		return $this->contactListFactory->create(
			$this->getObject(),
			$this->currentState,
			$this->mutation,
		);
	}

}
