<?php declare(strict_types = 1);

namespace App\PostType\Contact\Model;

use App\PostType\Contact\Model\Orm\Contact;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

final class ContactLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): ContactLocalization
	{
		$contactLocalization = new ContactLocalization();
		$this->orm->contactLocalization->attach($contactLocalization);
		$contactLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Contact();
			$contactLocalization->contact = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Contact);
			$contactLocalization->contact = $localizableEntity;
		}

		$this->orm->persistAndFlush($contactLocalization);

		return $contactLocalization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof ContactLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Contact);
		$this->orm->contactLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->contact->remove($parent);
		}

		$this->orm->flush();
	}

}
