<?php declare(strict_types=1);

namespace App\Console;

use App\Model\Orm\ConfiguratorTranslation\ConfiguratorTranslation;
use App\Model\Orm\ConfiguratorTranslation\ConfiguratorTranslationModel;
use App\Model\Orm\ConfiguratorTranslation\ConfiguratorTranslationRepository;
use App\Model\Orm\ConfiguratorTranslationExtender\ConfiguratorTranslationExtender;
use App\Model\Orm\ConfiguratorTranslationExtender\ConfiguratorTranslationExtenderRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\TranslatorDB;
use League\Csv\Reader;
use Nette\Utils\Json;
use Nextras\Orm\Collection\Expression\LikeExpression;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ImportCsvConfiguratorParametersCommand extends Command
{

	public function __construct(
		private readonly ConfiguratorTranslationRepository $configuratorTranslationRepository,
		private readonly ConfiguratorTranslationExtenderRepository $configuratorTranslationExtenderRepository,
		private readonly ConfiguratorTranslationModel $configuratorTranslationModel,
	)
	{
		parent::__construct();
	}

	protected static $defaultName = 'import:configurator:csv';


	public function readFile(mixed $record): void
	{
		if (trim($record['name']) === '') {
			return;
		}
		$originalName = strtolower($record['originalName']);
		$name = strtolower($record['name']);
		if (preg_match('/^polo/i', $name)) {
			$configuratorTranslations = $this->configuratorTranslationRepository->findBy([
				'name~' => LikeExpression::startsWith('síťka polo'),
				'type' => ConfiguratorTranslation::TYPE_VALUE,
			]);
			foreach ($configuratorTranslations as $configuratorTranslation) {
				$this->addExtenderCf($configuratorTranslation, $record);
			}
		} else {
			$configuratorTranslation = $this->configuratorTranslationRepository->getByChecked([
				'name' => $originalName,
				'type' => ConfiguratorTranslation::TYPE_VALUE,
			]);
			$this->addExtenderCf($configuratorTranslation, $record);
		}
	}

	protected function configure(): void
	{
		$this->setDescription('import csv with properties for configurator (delete after deploy to production)');

	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
//		$mutation = $this->mutationRepository->getByChecked(['langCode' => Mutation::DEFAULT_CODE]);
//		$this->translatorDB->reInit($mutation);

		$csv = Reader::createFromPath(TEMP_DIR . '/vlastnosti_latek.csv', 'r');
		$csv->setHeaderOffset(0);
		$csv->setEnclosure('"');
		$csv->setDelimiter(";");
		$records = $csv->getRecords();
		foreach ($records as $record) {
			dump($record);
			if ($record['name'] === 'die') {
				break;
			}
			$this->readFile($record);
		}

		$csv = Reader::createFromPath(TEMP_DIR . '/vlastnosti_latek2.csv', 'r');
		$csv->setHeaderOffset(0);
		$csv->setEnclosure('"');
		$csv->setDelimiter(";");
		$records = $csv->getRecords();
		foreach ($records as $record) {
			dump($record);
			$this->readFile($record);
		}

		return self::SUCCESS;
	}

	private function readParameters(array $record): array
	{
		$parameterNames = [
			'Compositions',
			'Abrasion resistance',
			'Pilling',
			'Size',
			'Flame resistance',
			'Sustainability',
			'Care/cleaning',
		];


		$parameters = [];
		foreach ($record as $parameterName => $value) {


			if (in_array($parameterName, $parameterNames)) {
				$parameterValues = [];
				foreach (explode("\n", $value) as $item) {
					if (trim($item) !== '') {
						$parameterValues[] = [
							'value' => trim($item)
						];
					}

//					$this->translatorDB->translate('configurator_' . $item);
				}

//				$this->translatorDB->translate('configurator_' . $parameterName);
				$parameter = [
					'parameter' => trim($parameterName),
					'values' => $parameterValues
				];
				$parameters[] = $parameter;
			}
		}

		return $parameters;
	}

	private function addExtenderCf(ConfiguratorTranslation $configuratorTranslation, array $record): void
	{
		if ($configuratorTranslation->extender === null) {
			return;
		}
		$cf = [
			'base' => [
				[
					'parameters' => $this->readParameters($record)
				]
			]
		];
		$data = Json::encode($cf);

		//this have to be here. \_(^_^)_/
		dump($configuratorTranslation->extender->getRawValue('customFieldsJson'));

		$configuratorTranslation->extender->setRawValue('customFieldsJson', $data);
		$configuratorTranslation->extender->setAsModified('customFieldsJson');
		$this->configuratorTranslationExtenderRepository->persistAndFlush($configuratorTranslation->extender);

		$this->configuratorTranslationModel->importExternalParameters($configuratorTranslation->extender);
	}
}
