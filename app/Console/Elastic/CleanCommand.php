<?php declare(strict_types=1);

namespace App\Console\Elastic;

use App\Model\Orm\EsIndex\EsIndexFacade;
use App\Model\Orm\Orm;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class CleanCommand extends Command
{
	protected static $defaultName = 'elastic:index:clean';

	public function __construct(
		private EsIndexFacade $esIndexFacade,
		private Orm $orm,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$this->setName('elastic:index:clean')
			->setDescription('Remove all unused indexes');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$output->writeLn('REMOVE FROM ORM');

		foreach ($this->orm->esIndex->findBy([
			'active' => 0
		]) as $esIndex) {
			$output->writeLn($esIndex->name);
			$this->esIndexFacade->delete($esIndex);
		}

		$output->writeLn('DONE');
		return self::SUCCESS;
	}

}

