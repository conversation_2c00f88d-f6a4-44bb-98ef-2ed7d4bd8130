<?php declare(strict_types=1);

namespace App\Console\Model\Feed;

class Storage
{

	public function __construct(
		private readonly string $basePath,
	)
	{
	}


	public function create(string $file, ?string $content = ''): void
	{
		if (!is_dir($this->basePath)) {
			mkdir($this->basePath);
		}

		file_put_contents($this->getFilePath($file), $content);
	}

	public function delete(string $file): void
	{
		$filePath = $this->getFilePath($file);
		if (file_exists($filePath)) {
			unlink($filePath);
		}
	}

	public function write(string $file, string $content): void
	{
		$filePath = $this->getFilePath($file);
		file_put_contents($filePath, $content, FILE_APPEND);
	}

	public function move(string $oldFile, string $newFile): void
	{
		$oldFilePath = $this->getFilePath($oldFile);
		$newFilePath = $this->getFilePath($newFile);
		rename($oldFilePath, $newFilePath);
	}


	private function getFilePath(string $file): string
	{
		return $this->basePath . '/' . $file;
	}

}
