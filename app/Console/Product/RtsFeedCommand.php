<?php declare(strict_types=1);

namespace App\Console\Product;

use App\Console\Model\Feed\Reseller\ResellerFeed;
use App\Console\Model\Feed\Reseller\RtsFeed;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class RtsFeedCommand extends Command
{
	use LockableTrait;

	public function __construct(

		private readonly RtsFeed $rtsFeed,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		private readonly MutationRepository $mutationRepository,
	)
	{
		parent::__construct(null);
	}


	protected static $defaultName = 'product:feed:rts';

	protected function configure(): void
	{
		$this->setDescription('generate xml for RTS');
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		if (!$this->lock()) {
			throw new Exception('The command is already running in another process.');
		}

		$mutations = $this->mutationRepository->findAll();
		foreach ($mutations as $mutation) {
			$output->writeln($mutation->langCode);
			$this->runCommand($mutation);
		}

		$output->writeln('DONE');

		return 0;
	}

	private function runCommand(Mutation $mutation): void
	{
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$this->rtsFeed->create($mutation);
	}

}
