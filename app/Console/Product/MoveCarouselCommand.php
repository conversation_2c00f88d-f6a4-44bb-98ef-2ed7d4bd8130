<?php

declare(strict_types=1);

namespace App\Console\Product;

use App\Model\CatalogTree;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\PostType\Page\Model\Orm\CommonTree;
use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use Nette\Utils\Json;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class MoveCarouselCommand extends Command
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly MutationRepository $mutationRepository,
		private readonly Orm $orm,
	)
	{
		parent::__construct();
	}

	protected static $defaultName = 'product:move:carousel';

	protected function configure(): void
	{
		$this->setDescription('Move CC items to CF');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->runCommand($output);

		return self::SUCCESS;
	}

	private function runCommand(OutputInterface $output): void
	{
		$this->productRepository->setPublicOnly(false);

		$mutationsIds = $this->mutationRepository->findBy([
			'langCode' => Mutation::CODE_EN
		])->fetchPairs(null, 'id');

		foreach ($mutationsIds as $mutationId) {
			$lastId = 0;
			do {
				$mutation = $this->mutationRepository->getByIdChecked($mutationId);
				$this->orm->setMutation($mutation);
				$fetched = $this->productLocalizationRepository->findBy([
					'id>' => $lastId,
					'mutation' => $mutation,
				])->orderBy('id')->limitBy(100)->fetchAll();

				foreach ($fetched as $productLocalization) {
					// do the work
					$localizationJsonData = Json::decode($productLocalization->getRawValue('customFieldsJson'));
					$productJsonData = Json::decode($productLocalization->getParent()->getRawValue('customFieldsJson'));
					if (isset($localizationJsonData->products_carousel_1)) {
						$productJsonData->products_carousel_1 = $localizationJsonData->products_carousel_1;
					}
//					if (isset($localizationJsonData->products_carousel_2)) {
//						$productJsonData->variants_carousel_2 = $localizationJsonData->products_carousel_2;
//					}
					$productLocalization->getParent()->setRawValue('customFieldsJson', Json::encode($productJsonData));
					$productLocalization->getParent()->setAsModified('customFieldsJson');

					$this->productRepository->persistAndFlush($productLocalization->getParent());
					$output->writeln((string) $productLocalization->id);
					$lastId = $productLocalization->id;
				}
				// release the entities from the memory
				$this->productLocalizationRepository->getModel()->clear();
			} while (!empty($fetched));
		}
	}

}
