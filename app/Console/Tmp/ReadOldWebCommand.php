<?php declare(strict_types=1);

namespace App\Console\Tmp;

use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationRepository;
use App\Model\Orm\Redirect\Redirect;
use App\Model\Orm\Redirect\RedirectRepository;
use Curl\Curl;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ReadOldWebCommand extends Command
{

	public function __construct(
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly ProductVariantLocalizationRepository $productVariantLocalizationRepository,
		private readonly MutationRepository $mutationRepository,
		private readonly LinkFactory $linkFactory,
		private readonly RedirectRepository $redirectRepository,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
	)
	{
		parent::__construct();
	}

	protected static $defaultName = 'tmp:read:old';

	protected function configure(): void
	{
		$this->setDescription('Read old web');
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->runCommand($output);
		return self::SUCCESS;
	}


	private function runCommand(OutputInterface $output): void
	{
		$curl = new Curl();

		foreach ([
			'https://www.ton.eu/cz/ton-produkty/' => 'cs',
			'https://www.ton.eu/sk/ton-produkty/' => 'sk',
			'https://www.ton.eu/de/produkte/' => 'de',
			'https://www.ton.eu/en/products/' => 'en',
			'https://www.ton.eu/fr/produits/' => 'fr',
			'https://www.ton.eu/pl/produkty/' => 'pl',
		 ] as $url=>$langCode)
		{
			$mutation = $this->mutationRepository->getByChecked(['langCode' => $langCode]);
			$this->orm->setMutation($mutation);
			$this->mutationHolder->setMutation($mutation);

			$response = $curl->get($url);
			foreach (explode("\n", $response) as $item) {
				$item = trim($item);
				if (strpos($item, 'class="tile__inner"')) {
					//<a href="/cz/ton-produkty/detail/zidle-la-zitta1/" class="tile__inner" title="Židle La Zitta (313 301) - Více">
//				dump($item);
					if (preg_match('/<a href="(.*?)".*\((.*?)\) /', $item, $matches)) {
						if (!isset($matches[1]) || !isset($matches[2])) {
							dump($matches);
						}
//					dump($matches);
						$oldUrl = $matches[1];
						$code = str_replace(' ', '', $matches[2]);
						[$productLocalization, $variant] = $this->getValidVariant($code, $mutation);
						if ($productLocalization !== null && $variant !== null) {
							$newLink = $this->linkFactory->linkTranslateToNette(
								$productLocalization,
								['v' => $variant->id, 'mutation' => $mutation],
								absolute: false,
							);
							$redirect = $this->redirectRepository->getBy([
								'oldUrl' => $oldUrl
							]);
							if ($redirect === null) {
								$redirect = new Redirect();
								$this->redirectRepository->attach($redirect);
							}
							$redirect->oldUrl = $oldUrl;
							$redirect->code = 301;
							$redirect->newUrl = $newLink;

							$this->redirectRepository->persistAndFlush($redirect);
//							dump($oldUrl . ' --> ' . $newLink);
						} else {
							dump('not found ' . $code . ' - '. $oldUrl);
						}
					}
				}
			}
		}
	}

	private function getValidVariant(string $code, Mutation $mutation): array
	{
		$code = $this->findNewCode($code);
		$productLocalization = null;
		$variantLocalization = $this->productVariantLocalizationRepository->getBy([
			'mutation' => $mutation,
			'variant->code' => $code,
		]);
		$variant = $variantLocalization?->variant;
		$product = $variant?->product;
		if ($product !== null) {
			$productLocalization = $this->productLocalizationRepository->getBy([
				'mutation' => $mutation,
				'product' => $product,
			]);
		}

		return [$productLocalization, $variant];
	}

	private function findNewCode(string $code): string
	{
		$map = [
			'421706' => '4me706',
			'421707' => '4me707',
			'421135' => '4ug135',
			'421722' => '4mj722',
			'421723' => '4mj723',
			'421724' => '4mj724',
			'421725' => '4mj725',
			'421726' => '4mj726',
			'421727' => '4mj727',
			'421421' => '4mj421',
			'421422' => '4mj422',
			'421423' => '4mj423',
			'421628' => '4m5628',
			'421629' => '4m5629',
			'421630' => '4m5630',
			'421631' => '4m5631',
			'421632' => '4m5632',
			'421633' => '4m5633',
			'421634' => '4m5634',
			'421635' => '4m5635',
			'421636' => '4m5636',
			'421637' => '4m5637',
			'421473' => '4uj473',
			'421474' => '4uj474',
			'421475' => '4uj475',
			'421460' => '4m5460',
			'421461' => '4m5461',
			'421462' => '4m5462',
			'421463' => '4m5463',
			'421464' => '4m5464',
			'421465' => '4m5465',
			'421466' => '4m5466',
			'421467' => '4m5467',
			'421468' => '4m5468',
			'421470' => '4uj470',
			'421448' => '4me448',
			'421446' => '4m5446',
			'421406' => '4mj406',
			'421409' => '4mj406',
			'421442' => '4mj406',
			'421443' => '4mj406',
			'421271' => '4m5271',
			'421275' => '4m5275',
			'421276' => '4m5276',
			'421277' => '4m5277',
			'421278' => '4m5278',
			'421280' => '4m5280',
			'421716' => '4n8716',
			'421476' => '4m5476',
			'421152' => '4m5152',
			'421252' => '4m5252',
			'421681' => '4u1681',
			'421683' => '4u1683',
			];

		if (isset($map[$code])) {
			return $map[$code];
		}
		return $code;

	}


}
