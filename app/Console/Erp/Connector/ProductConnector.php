<?php declare(strict_types = 1);

namespace App\Console\Erp\Connector;

use App\Console\Erp\Exception\LoggedException;
use App\Model\ConfigService;
use App\Model\Orm\Orm;
use App\Model\Sentry\SentryLogger;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;

class ProductConnector extends RestConnector implements ReadCollection
{

	public function __construct(
		private readonly string $path,
		public readonly string $remotePrimaryKeyName,
		Orm $orm,
		ConfigService $configService,
		LoggerManager $loggerManager,
		SentryLogger $sentryLogger)
	{
		parent::__construct($orm, $configService, $loggerManager, $sentryLogger);
	}

	/**
	 * @throws LoggedException
	 */
	public function getRows(Query $query): ArrayHash
	{
		$parts = [];
		$parts[] = $this->path;
		$parts[] = '1/WEB_POVOL'; // only web products
		$parts[] = $query->idFrom;
		$parts[] = $this->remotePrimaryKeyName;
		$parts[] = $query->idTo;

		$pathWithLimits = implode('/', $parts);

		$IDVYROBEKs = [];
		$rows = $this->callApi('GET', $pathWithLimits);
		foreach ($rows->data as $row) {
			$IDVYROBEKs[] = $row->IDVYROBEK;
		}

		sort($IDVYROBEKs);
		$chairSizeByProductId = $this->getSubData('TTE_WZIDLEROZMERY', $IDVYROBEKs);
		$tableSizeByProductId = $this->getSubData('TTE_WSTULROZM', $IDVYROBEKs);
		$productLineByProductId = $this->getSubData('TTE_WSPOJRAD', $IDVYROBEKs);
		$stackableByProductId = $this->getSubData('TTE_WSTOHOVATELNOST', $IDVYROBEKs);
		$packageByProductId = $this->getSubData('TTE_WBALENI', $IDVYROBEKs);
		$pricesProductId = $this->getSubData('TTE_WCENIKH', $IDVYROBEKs);

		foreach ($rows->data as $key=>$row) {
			if ( $row->Skupina === 'a_SKUP_ZIDLE' && isset($chairSizeByProductId[$row->IDVYROBEK])) {
				$row->sizeData = $chairSizeByProductId[$row->IDVYROBEK];
			} else if ( $row->Skupina === 'b_SKUP_STUL' && isset($tableSizeByProductId[$row->IDVYROBEK])) {
				$row->sizeData = $tableSizeByProductId[$row->IDVYROBEK];
			} else if ( $row->Skupina === 'c_SKUP_VESAK' && isset($chairSizeByProductId[$row->IDVYROBEK])) {
				$row->sizeData = $chairSizeByProductId[$row->IDVYROBEK];
			}

			if (isset($productLineByProductId[$row->IDVYROBEK])) {
				$row->lineData = $productLineByProductId[$row->IDVYROBEK];
			}

			if (isset($stackableByProductId[$row->IDVYROBEK])) {
				$row->stackableData = $stackableByProductId[$row->IDVYROBEK];
			}
			if (isset($packageByProductId[$row->IDVYROBEK])) {
				$row->packageData = $packageByProductId[$row->IDVYROBEK];
			}
			if (isset($pricesProductId[$row->IDVYROBEK])) {
				$row->priceData = $pricesProductId[$row->IDVYROBEK];
			}

			$rows[$key] = $row;
		}

		return $rows;
	}

	public function getPaging():array
	{
		$maxId = $this->getMaxId($this->path, $this->remotePrimaryKeyName);
		return self::getPagingForRequest($maxId);
	}


	public function getRemovePrimaryKey(): string
	{
		return $this->remotePrimaryKeyName;
	}

	private function getSubData(string $basePath, array $IDVYROBEKs): array
	{
		$sizeByProductId = [];
		if ($IDVYROBEKs !== []) {
			$sizes = $this->callApi('GET', implode('/', [
				$basePath,
				$IDVYROBEKs[0],
				'IDVYROBEK',
				$IDVYROBEKs[count($IDVYROBEKs) - 1] + 1,
			]));
			foreach ($sizes->data as $sizeRow) {
				$sizeByProductId[$sizeRow->IDVYROBEK][] = $sizeRow;
			}
		}

		return $sizeByProductId;
	}

}
