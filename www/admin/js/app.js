(function($)
{
	/*******************************************************
		APP
	********************************************************/
	var App = window.App =
	{
		images: new sk.widgets.AttachedImages(),
		statusRow: new sk.widgets.StatusRow( $('<div class="app-status"><div class="bar"><div class="text"></div></div></div>').appendTo('body').children().hide() ).init(),

		options: {

		},
		getPlace: function(context)
		{
			var place = $(context).data('place')
			return place ? $( '#' + $(context).data('place') ) : $([]);
		},
		confirm: function(text)
		{
			return confirm(text)
		},
		sort: function(target)
		{
			//console.log('aa');
			// změním sort inputy
			$(target)
				.find('.inp-sort')
				.val(function(i, old)
				{
					return i
				})
		},
		run: function(options)
		{
			var app = this;

			var $W = $(window);
			var $D = $(document);
			var $B = $('body');

			//pomocna promenna pro klonovani inputu
			var globalCounter = 10000;

			//pomocna promenna pro focus po create node
			var isCreate = false;

			app.options = $.extend(true, app.options, options);
			appSessionName = app.options.sessionName

			// change URL (requires HTML5)
			if (window.history && history.replaceState) {
				history.replaceState({href: window.location.href}, '', window.location.href);
			}

            // HTML 5 popstate event
            $(window).bind('popstate', function(event) {
                $.nette.href = null;
                $.post(event.originalEvent.state.href, $.nette.success);
            });


			$.ajaxSetup({
				success: $.nette.success,
				dataType: "json"
			});

			// Thickbox
			var thickbox = new sk.widgets.Skbox({
				'width': 2000,
				'height': 2000,
				'padding': [0, 0],
				'margin': [40, 40],
				'ajax': {
					'data': {
						'thickbox': 1,
						// 'open': true
					}
				}
			}).init();

			$(thickbox)
				.on('afterload.skbox', function()
				{
					this.$box.trigger('contentload');
				})
				.on('ajaxsuccess.skbox', function(event, response)
				{
					//TODO - presmerovani po prihlaseni - nevim kam

					try
					{
						response = JSON.parse(response);


						$.nette.success(response);
						event.preventDefault();
					}
					catch(e)
					{
						//console.log('neni JSON');
					}

				});


			var datepickerControl = {
				create: function(tp_inst, obj, unit, val, min, max, step){
					$('<input class="inp-text ui-timepicker-input" value="'+val+'" style="width:50%">')
						.appendTo(obj)

						.spinner({
							min: min,
							max: max,
							step: step,
							change: function(e,ui){ // key events
								// don't call if api was used and not key press
								if(e.originalEvent !== undefined)
									tp_inst._onTimeChange();
								tp_inst._onSelectHandler();
							},
							spin: function(e,ui){ // spin events
								tp_inst.control.value(tp_inst, obj, unit, ui.value);
								tp_inst._onTimeChange();
								tp_inst._onSelectHandler();
							}
						});
					return obj;
				},
				options: function(tp_inst, obj, unit, opts, val){
					if(typeof(opts) == 'string' && val !== undefined)
						return obj.find('.ui-timepicker-input').spinner(opts, val);
					return obj.find('.ui-timepicker-input').spinner(opts);
				},
				value: function(tp_inst, obj, unit, val){
					if(val !== undefined)
						return obj.find('.ui-timepicker-input').spinner('value', val);
					return obj.find('.ui-timepicker-input').spinner('value');
				}
			};


			$D
				.on('click', '.js-tmp-add', function(event) {
					event.preventDefault();
					const htmlToPlace = $(this).data('html');
					let r = Math.random().toString(36).substring(7);
					$(this).parent().append(htmlToPlace.replace('newItemMarker', 'newItemMarker_' + r));
					$(this).parent().trigger('contentload');
				})

				// Ajax status
				.ajaxStart(function( event, request, settings )
				{
					app.statusRow.text({text: 'Načítám'});
					app.statusRow.removeClass();
					app.statusRow.show();
				})
				.ajaxSuccess(function( event, request, settings )
				{
					//console.log(request);
					//$.nette.success(JSON.parse(request.responseText));

					setTimeout(function()
					{
						app.statusRow.text({text: 'Hotovo', klass: 'ok'});
						app.statusRow.hide(700);
					}, 400)
				})
				.ajaxError(function( event, request, settings)
				{
					app.statusRow.text({text: 'Error', klass: 'error'});
					app.statusRow.hide(700);
				})
				// Toggle
				.on('click', '[data-toggle]', function(event)
				{
					event.preventDefault();

					var toggle = $(this).data('toggle');
					var $all = $('[data-toggle="'+ toggle +'"]');
					var index = $all.index( this );

					$(this)
						.toggleClass('open')
						.next()
							.slideToggle(500);

					if( $(this).hasClass('open') )
					{
						$.cookie(toggle, index);
					}
					else
					{
						$.removeCookie(toggle);
					}

					$all
						.not(this)
						.removeClass('open')
							.next()
								.slideUp(500)
				})
				//copy LI
				.on('click', 'a[data-copy]', function(event)
				{
					event.preventDefault();

					var copy = $(this).data('copy');
					var $target = $('ul[data-copy='+copy+']');
					if (copy == 'comments') {
						var $newli = $($target.data('pattern').replace(/XXX/g, 'new-comment-' + globalCounter++));
				    } else {
						var $newli = $($target.data('pattern').replace(/XXX/g, globalCounter++));
				    }

					// Add and anim new item
					$newli
						.hide()
						.appendTo( $target )
						.slideDown( $target.is(':hidden') ? 0 : 250 )
						.trigger('contentload');

					// Show target
					if( $target.find('li').length && $target.is(':hidden') )
					{
						$target.closest('.holder').slideDown(250);
					}

					$('input:first', $newli).focus();
				})

				.on('click', '.ajaxApend', function(event) {
					event.preventDefault();
					$.ajax({
						url: this.href,
						type: 'get',
						dataType: 'json',
						success: function(response) {
							for (var i in response.snippets) {

								var $target = $('#' + i);
								if ($target.attr('data-append')) {
									//append
									$target.append(response.snippets[i]);
								} else {
									//replace
									$target.html(response.snippets[i]);
								}
							}
						}
					});
				})

				.on('change')

				.on('keydown', '.crossroad-attached input:text', function(event)
				{
					if(event.keyCode === 13)
					{
						event.preventDefault();
						$(this).closest('.crossroad-attached').find('.ft .btn').trigger('click');
					}
				})
				// Pair input value
				.on('change', 'input.inp-text[data-pair]', function(event)
				{
					var val = $(this).data('value');
					var change = true;
					var $pairs = $( $(this).data('pair') );

					$pairs
						.each(function(index)
						{
							if( val !== this.value )
							{
								change = false;
								return;
							}
						})

					if(change)
					{
						$pairs.val( this.value );
					}

					$(this).data('value', this.value);
				})
				// automatic regerenate alias for non edited item
				.on('change', 'input.inp-text[data-regeneratealias=true]', function(event)
				{
					$('#regeneratealias').trigger('click');
				})
				.on('click', '#regeneratealias', function(event)
				{
					event.preventDefault();
					$('.alias').addClass('loading');


					var data = {
						'name': $('.aliasName').val(),
						'lg': $(this).data('lg'),
						'id': $(this).data('id'),
					}

					// if ($(this).data('id')) {
					// 	console.log("ano");
					// } else {
					// 	console.log("NE");
					// }

					$.ajax({
						url: $(this).data('action'),
						dataType: 'text',
						data: data,
						success: function(data, textStatus, xhr)
						{
							$('.alias').val(data);
							$('.alias').removeClass('loading');
						},
						error: function(xhr, textStatus, errorThrown)
						{

						}
					});
				})
				.on('change', '#menu-params input', function(event)
				{
					if( this.checked )
					{
						$.cookie('param-'+this.id, 1);
					}
					else
					{
						$.removeCookie('param-'+this.id);
					}

					$('tr.'+this.id).toggle( this.checked );

				})
				.on('click', '.ajax', function(event)
				{
					event.preventDefault();
					$.post($.nette.href = this.href);
				})
				.on('submit', '.js-only-changes', function(event)
				{
					// event.preventDefault();
					// $( "input[value='Hot Fuzz']" )
					$($(this).serializeArray()).each(function(index, el) {
						var $input = $("input[name='" + el.name + "']");
						if (el.value == $input.data('value')) {
							$input.prop('disabled', true);
							// console.log('ano');
						}
						// else {
						// 	// console.log('ne '+ el.value );
						// }
					});
				})


				// Ajax form
				.on('submit', '.ajax-form', function(event)
				{
					event.preventDefault();

					$.nette.href = null;

					// TODO smazat
					// var $place = app.getPlace(this);

					$.ajax({
						url: this.action,
						type: this.method.toUpperCase(),
						dataType: 'json',
						data: $(this).serialize(),
						/*success: function(data, textStatus, xhr)
						{
							$.nette.success(data);

						},
						*/
						error: function(xhr, textStatus, errorThrown)
						{

						}
					});


				})
				// Show/Hide box by param type
				.on('change', '.select-param-type', function(event)
				{
					var actualType = $(this).val();

					$('.box-param-type').each(function(index, el)
					{
						var types = $(this).data('type');

						$(this).toggle( $.inArray(actualType, types) > -1 );
					});
				})
				// Attach images
				.on('click', '.library-images li', function(event)
				{
					event.preventDefault();

					var $image = $(this);

					// obrazky u hodnot parametru
					var $valueId = $image.data("valueid");
					if ($valueId == undefined) {
						$valueId = "";
					}

					// remove
					if( $image.hasClass('selected') )
					{
						var id = $image.data('id');
						$('#attached-images'+$valueId+' li[data-id="'+ id +'"]').remove();

						$image
							.removeClass('selected');
					}
					// append
					else
					{
						// je to hodnota parametru - smazu predesle hodnoty
						if ($valueId) {
							$('#attached-images'+$valueId).empty();
						}

						// TOTO: hotfix
						// v thickboxu dochází k mazání přidaného obrázku pokud dojde k následnému klonování
						if ($image.is('.loading') && $image.closest('.skbox-window').length) {
							var prev = $image.prev();
							var parent = $image.parent();
							setTimeout(function () {
								if (prev.length) {
									$image.insertBefore(prev);
								} else {
									$image.prependTo(parent);
								}
							}, 100);
						}

						var clone = $image.clone(true, true);
						$(clone).appendTo('#attached-images' + $valueId);

						app.sort($('#attached-images'+$valueId));

						$image
							.addClass('selected');
					}
				})
				.on('click', '.library-img-special li', function(event)
				{
					event.preventDefault();

					var $image = $(this);

					// obrazky u hodnot parametru
					var $valueId = $image.data("valueid");
					if ($valueId == undefined) {
						$valueId = "";
					}

					// remove
					if( $image.hasClass('selected') )
					{
						var id = $image.data('id');
						$('#attached-images'+$valueId+' li[data-id="'+ id +'"]').remove();

						$image
							.removeClass('selected');
					}
					// append
					else
					{
						// je to hodnota parametru - smazu predesle hodnoty
						if ($valueId) {
							$('#attached-images'+$valueId).empty();
						}

						// $image
						// 	.clone(true, true)
						// 	.appendTo('#attached-images'+$valueId);

						// TOTO: hotfix
						// v thickboxu dochází k mazání přidaného obrázku pokud dojde k následnému klonování
						if ($image.is('.loading') && $image.closest('.skbox-window').length) {
							var prev = $image.prev();
							var parent = $image.parent();
							setTimeout(function () {
								if (prev.length) {
									$image.insertBefore(prev);
								} else {
									$image.prependTo(parent);
								}
							}, 100);
						}

						var clone = $image.clone(true, true);
						$(clone).appendTo('#attached-images' + $valueId);


						app.sort($('#attached-images'+$valueId));

						$image
							.addClass('selected');
					}
				})
				.on('click', '.library-img-special-content li', function(event)
				{
					alert('content');
				})
				.on('click', 'tr.clickable', function(event)
				{
					if( !$(event.target).is('a')  )
					{
						event.preventDefault();
						window.location = $(this).find('a').get(0).href;
					}
				})
				.on('mouseenter', '.col-side', function(event)
				{
					$B.addClass('menu-hover');
				})
				.on('mouseleave', '.col-side', function(event)
				{
					$B.removeClass('menu-hover');
				})
				// Instances on content load
				.on('contentload', function(event)
				{
					// Select images in library
					// TODO #attached-images je na pevno - možná by to chtělo udělat variabilní
					$('.library-images', event.target).each(function()
					{
						var selectedID = $('#attached-images li').map(function(){ return $(this).data('id') }).get();

						if(selectedID.length)
						{
							$(this).find('li[data-id="'+ selectedID.join('"], li[data-id="') +'"]').addClass('selected');
						}
					});


					$('#frm-editForm-isTopProduct').on('click', function() {
						if ($('#frm-editForm-isTopProduct').is(':checked')) {
							$('#alexTipText').show();
						}
						else {
							$('#alexTipText').hide();
						}
					});

					$('#frm-editForm-isRent').on('click', function () {
						if ($('#frm-editForm-isRent').is(':checked')) {
							$('#rentBox').show();
						}
						else {
							$('#rentBox').hide();
						}
					});

					$('#frm-editForm-isInPrepare').on('click', function () {
						if ($('#frm-editForm-isInPrepare').is(':checked')) {
							$('#isInPrepareBox').show();
						}
						else {
							$('#isInPrepareBox').hide();
						}
					});


					// $('.library-img-special', event.target).each(function()
					// {
					// 	var selectedID = $('#attached-images li').map(function(){ return $(this).data('id') }).get();
					//
					// 	if(selectedID.length)
					// 	{
					// 		$(this).find('li[data-id="'+ selectedID.join('"], li[data-id="') +'"]').addClass('selected');
					// 	}
					// });

					$('.inp-datetime-entry', event.target).datetimepicker({
						controlType: datepickerControl,
						showOtherMonths: true,
						dateFormat: "yy-mm-dd",
						timeFormat: "HH:mm:ss"
					});

					$('.inp-datetime-entry2', event.target).datetimepicker({
						controlType: datepickerControl,
						showOtherMonths: true,
						dateFormat: "dd. mm. yy",
						timeFormat: ""
					});

					$('.inp-datetime-entry-hour', event.target).datetimepicker({
						controlType: datepickerControl,
						showOtherMonths: true,
						dateFormat: "yy-mm-dd",
						timeFormat: "HH"
					});

					$('.inp-datetime-entry-only-hour', event.target).datetimepicker({
						timeOnly: true,
						format: 'LT'
					});

					// Find and active toggle
					$('h4[data-toggle]', event.target)
						.each(function(i)
						{
							var toggle = $(this).data('toggle');
							var cookieCurrent = $.cookie(toggle);

							if(cookieCurrent)
							{
								// z cookie dostanu aktivní index
								var index = parseInt(cookieCurrent);
								// aktivace toggle
								$('[data-toggle="'+ toggle +'"]')
									.eq(index)
									.addClass('open')
									.next()
										.show();
							}
						});

					// Param select call
					// TODO možná by bylo lepší kdyby to chodilo už ze serveru
					$('.select-param-type', event.target).trigger('change')

					// Tabs
					$('.menu-tabs', event.target)
						.each(function(i)
						{
							if($(this).hasClass('js-disable-tabs')) {
								return;
							}
							var current = 0;
							var cookieCurrent = $.cookie('activetab');

							if(cookieCurrent && typeof cookieCurrent === 'string')
							{
								current = parseInt(cookieCurrent)
							}

							var $this = $(this);
							var Tabs = new sk.widgets.Tabs($this, {
									'current': current
								}).init();

							$(Tabs)
								.on('activate', function()
								{
									$.cookie('activetab', this.currentIndex);
								});
					    });

					// JStree
					$(function()
					{
						var plugins = ['html_data', 'ui', 'crrm', 'cookies', 'types', 'contextmenu']

						if( !$(event.target).closest('.skbox-window').length )
						{
							plugins.push('dnd');
						}

						$('.menu-tree', event.target).each(function()
						{
							var id = this.id;
							var $tree = $(this);

							var contextmenuitems = {
								'create' : {
									// The item label
									"label"				: "Vložit",
									// The function to execute upon a click
									"action"			: function (obj) {
										//console.log(obj);

										$tree.jstree("create",'#'+obj.attr('id'), "last", "Enter a new name", false, true);

										//return true;
									},
									// All below are optional
									"_class"			: "class",	// class is applied to the item LI node
									"separator_before"	: false,	// Insert a separator before the item
									"separator_after"	: false,		// Insert a separator after the item
									// false or string - if does not contain `/` - used as classname
									"icon"				: false
								},
								'rename': false,
								'remove': false,
								'ccp': false
							}

							// if($('#parametertree').length)
							// {
							// 	contextmenuitems.createcat = {
							// 		// The item label
							// 		"label"				: "Vložit kategorii",
							// 		// The function to execute upon a click
							// 		"action"			: function (obj) {
							// 			//console.log(obj);
							//
							// 			$tree.jstree("create", '#'+obj.attr('id'), "last", "Enter a new category name", false, true);
							//
							// 			//return true;
							// 		},
							// 		// All below are optional
							// 		"_class"			: "class",	// class is applied to the item LI node
							// 		"separator_before"	: false,	// Insert a separator before the item
							// 		"separator_after"	: false,		// Insert a separator after the item
							// 		// false or string - if does not contain `/` - used as classname
							// 		"icon"				: false
							// 	};
							// }

							$tree.jstree({
								'core': {
									'animation': 400,
									'initially_open': [ 't1']
								},
								'ui': {
									"select_limit": 1
								},
								'cookies': {
									'save_selected': false,
									"save_opened": "node_opened_" + id
								},
								/*
								 'types': {
								 'max_depth': 2
								 },
								 'crrm': {
								 'move': {
								 "check_move" : function(data) {

								 console.log(data);
								 console.log(this.get_path(data.np[0]));
								 // TODO  - dodelat omezeni u parametru
								 /*
								 var p = this._get_parent(data.o);
								 //You cannot move a node with no parents
								 if(p == -1) {
								 return false;
								 }
								 //You cannot move a child to the root
								 else if(!this.get_path(data.np[0])) {
								 return false;
								 }
								 //You cannot move a node deeper than 1 level into the tree
								 else if(this.get_path(data.np[0]).length > 1) {
								 return false;
								 }
								 //* /

								 //You cannot move a child to the root
								 if(!this.get_path(data.np[0]) && this._get_parent(data.o) != -1) {
								 return false;
								 }
								 //You cannot move a node deeper than 1 level into the tree
								 else if(this._get_parent(data.o) == -1 && this.get_path(data.np[0]).length >= 1) {
								 return false;
								 }

								 return true;

								 }
								 }
								 },
								 */



								'contextmenu' : {
									'items': contextmenuitems
								},
								'plugins': plugins

							})
							.bind("select_node.jstree", function (event, data)
							{
								var a = $('a', data.rslt.obj);
								$.post($.nette.href = a.attr("href"), $.nette.success);
							})
							.bind("move_node.jstree", function (event, data)
							{

								if (true) //confirm("Jsi si jistý?"))
									{
										var node = data.rslt.o.attr('id').slice(1);

										$.ajax({
											url: $tree.data('move'), //"/superadmin/pages/move",
											data: {
												node: node,
												to: data.rslt.r.attr('id').slice(1),
												rel: data.rslt.p
											}
										}).done(function(ret){
												//console.log(ret);
											}).fail(function(){
												$.jstree.rollback(data.rlbk);
											});
									}
									else
									{
										$.jstree.rollback(data.rlbk);
									}

							})
							.bind("after_close.jstree", function(event, data) {
								// FIX zavirani pod polozek stromu

								var nodeArray;
								if ($.cookie("node_opened_")) {
									nodeArray = $.cookie("node_opened_").split(',');
								} else if ($.cookie("node_opened_librarytree")) {
									nodeArray = $.cookie("node_opened_librarytree").split(',');
								}
								$(data.rslt.obj).find('.jstree-open').each(function () {
									// najdi vsechny otevrene potomky -> smaz jejich cookies

									var idToColse = '#' + ($(this).attr('id'));
									nodeArray = nodeArray.filter(function(currentValue,index,arr) {
										if (currentValue == idToColse) {
											return false
										}
										return true
									})
								})
								if (nodeArray) {
									$.cookie("node_opened_", nodeArray.join(','))
								}
							})

							.bind("create_node.jstree", function (event, data)
							{
								var nodename = $.trim( data.rslt.obj.text() )
								if( $(event.target).closest('.skbox-window').length )
								{
									nodename = prompt('Zadejte jméno složky:', 'Nová složka');
								}

								if (nodename)
								{
									$.ajax({
										url: $tree.data('create'), //"/superadmin/pages/create",
										data: {
											to: data.rslt.parent != -1 ? data.rslt.parent.attr('id').slice(1) : '',
											name: nodename
										}
									}).done(function(ret){
										data.rslt.obj.attr('id', 't'+ret);
										var add = '?';
										if ($tree.data('click').indexOf('?') != -1) add = '&';

										$('a', data.rslt.obj).attr('href', $tree.data('click')+add+'id='+ret);//'/superadmin/pages?id='+ret);
										$('a', data.rslt.obj).html('<ins class="jstree-icon">&nbsp;</ins>'+nodename);

										isCreate = true;
										$tree.jstree("select_node", "#t"+ret, true);

										//data.rslt.obj.trigger('select_node.jstree', data);
									}).fail(function(){
										$.jstree.rollback(data.rlbk);
									});
								}
								else
								{
									$.jstree.rollback(data.rlbk);
								}

							});
						});


                        $('.menu-tree-cats', event.target).each(function()
                        {
                            var id = this.id;
                            var $tree = $(this);

                            $tree.jstree({
                                'core': {
                                    'animation': 0,
                                    'open_all': true
                                },



                                'ui': {
                                    "select_limit": 1,
                                    theme_name : "checkbox"

                                },
                                'cookies': {
                                    'save_selected': false,
                                    "save_opened": "node_opened_" + id,
                                },

                                "checkbox" : {
                                    "keep_selected_style" : false,
                                    "three_state" : false,
                                    "whole_node" : true,
                                    "real_checkboxes": true,
                                    "real_checkboxes_names" : function(n){
                                        return [("category" + (n[0].id)), 1];
                                    },
                                    two_state: true
                                },


                                "plugins" : [ plugins, "checkbox", "ui" ]


                            })
							.on('loaded.jstree', function() {
								$tree.jstree('open_all');
							});


                        });

						$('.menu-tree-templates', event.target).each(function()
						{
							var id = this.id;
							var $tree = $(this);

							var plugins = ['html_data', 'ui', 'crrm', 'cookies', 'types']

							$tree.jstree({
									'core': {
										'animation': 400,
										'initially_open': [ 't1']
									},
									'ui': {
										"select_limit": 1
									},
									'cookies': {
										'save_selected': false,
										"save_opened": "node_opened_" + id
									},


									"plugins" : plugins


								})
								.bind("select_node.jstree", function (event, data)
								{
									var a = $('a', data.rslt.obj);
									$('input[name=finalTreeId]').val($(a).data('id'))
								});


						});


					});

					var onMenuItemEnter = function() {
						$(this).addClass('js-selected');
					}
					var onMenuItemLeave = function() {
						$(this).removeClass('js-selected');
					}

					// Sortable
					$('.sortable', event.target)
						.sortable({
							cursor: 'move',
							handle: '.js-handle'
						})
						//.disableSelection()		//TODO ve firefoxu nejde kliknout do inputu
						.on('sortstart', function(event, ui){
							var $menu = $(this).closest('.skbox-content, .col-main').find('.menu-tree');
							var $item = $(ui.item.context);

							if ($menu.length) {
								$item.addClass('is-grabbing');
								$menu.on('mouseenter', 'a', onMenuItemEnter);
								$menu.on('mouseleave', 'a', onMenuItemLeave);
							}
						})
						// Po ukončení sortu
						.on('sortstop', function( event, ui )
						{

							var $menu = $(this).closest('.skbox-content, .col-main').find('.menu-tree');
							var $item = $(ui.item.context);

							if ($menu.length) {
								$menu.off('mouseenter', 'a', onMenuItemEnter);
								$menu.off('mouseleave', 'a', onMenuItemLeave);
								$item.removeClass('is-grabbing');
								$selectedItem = $menu.find('.js-selected');
								$menu.find('a').removeClass('js-selected');

								if ($selectedItem.length) {
									var id = $selectedItem.data('id');
									var imgId = $item.data('id');
									if (id) {
										console.log(`slozka: ${id}, obrazek: ${imgId}`);
										$(ui.item.context).remove();
										// TODO nejaky ajax call, kteremu se preda id
										$.ajax({
											url: '/superadmin/library/move-image', //"/superadmin/pages/move",
											data: {
												folder: id,
												image: imgId,
											}
										}).done(function(ret){
											//console.log(ret);
										}).fail(function(){
											$.jstree.rollback(data.rlbk);
										});
									}
								}
							}

							app.sort(event.currentTarget);
						});


					$('.surveySortable', event.target)
						.sortable({
							items: '> div > .js-li',
							cursor: 'move',
							handle: '.js-handle'
						})
						//.disableSelection()		//TODO ve firefoxu nejde kliknout do inputu
						// Po ukončení sortu
						.on('sortstop', function( event, ui )
						{
							app.sort(event.currentTarget);
						});



					// Attached images
					$('.crossroad-images', event.target)
						// sortable start reset grid
						.on('sortstart', function( event, ui )
						{
							ui.item.add( ui.item.siblings() )
								.find('.active')
								.removeClass('active')
								.css('padding-bottom', '')
								.find('.detail')
									.css('height', '')
						})
						// grid crossroad
						.filter('.crossroad-grid')
							.grid()

					// Wysiwyg editor init
					// base setup
					var tinyMceConfig = {
						entity_encoding : "raw",
						remove_linebreaks: "false",
						gecko_spellcheck: false,
						keep_styles: true,
						accessibility_focus: true,
						tabfocus_elements: 'major-publishing-actions',
						media_strict: false,
						paste_remove_styles: false,
						paste_remove_spans: false,
						paste_strip_class_attributes: 'none',
						paste_text_use_dialog: true,
						relative_urls : false,

						setup: function (editor) {

							// function skButton()
							// {
							// 	editor.insertContent('' +
							// 		'<a href="" class="btn">' +
							// 		'<span class="btn__text">Odkaz</span>'+
							// 		'</a>'+
							// 		'');
							// }
							// editor.ui.registry.addButton('skButton', {
							// 	text: 'Tlačítko',
							// 	tooltip: "Tlačítko",
							// 	onAction: skButton
							// });

							// function rememberBox() {
							// 	editor.insertContent('' +
							// 		'<p class="b-message">' +
							// 		'<strong class="b-message__title h3">'+
							// 		'Zapamatujte si'+
							// 		'</strong>'+
							// 		'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Sed vel lectus. Donec odio tempus molestie, porttitor ut, iaculis quis, sem. Maecenas fermentum, sem in pharetra pellentesque, velit turpis volutpat ante, in pharetra metus odio a lectus.'+
							// 		'</p>');
							// }
							// editor.ui.registry.addButton('rememberBox', {
							// 	text: 'Zapamatujte si',
							// 	tooltip: "Zapamatujte si",
							// 	onAction: rememberBox
							// });

							function skButtonWindow() {
								// Načti defaultní hodnoty
								var btn = editor.selection.getNode().closest('.btn');
								var link = btn ? btn.getAttribute('href') : '';
								var text = editor.selection.getNode().textContent;
								var blank = btn && btn.getAttribute('target') ? true: false;
								var type = btn && btn.classList[1] ? btn.classList[1] : '';

								editor.windowManager.open({
									title: 'Vložit tlačítko',
									body: {
										type: 'panel',
										items: [
											{
												type: 'input',
												name: 'link',
												label: 'URL',
											},
											{
												type: 'input',
												name: 'text',
												label: 'Text tlačítka',
											},
											{
												type: 'selectbox',
												name: 'type',
												label: 'Vyberte styl',
												items: [
													{ value: '', text: 'Defaultní' },
													{ value: 'btn--secondary', text: 'Sekundární' },
													{ value: 'btn--gray', text: 'Šedé' },
												],
											},
											{
												type: 'checkbox',
												name: 'blank',
												label: 'Otevřít odkaz v novém okně',
											},
										],
									},
									initialData: {
										link: link,
										text: text,
										blank: blank,
										type: type,
									},
									buttons: [
										{
											type: 'cancel',
											text: 'Close',
										},
										{
											type: 'submit',
											text: 'Save',
											primary: true,
										},
									],
									onSubmit: function(api) {
										let data = api.getData();
										var newBtn =`<a href="${data.link}" class="btn btn--md ${data.type}" ${data.blank ? 'target="_blank"' : null}>
											<span class="btn__text"><span>${data.text}</span></span></a>`;

										// Pokud tlačítko existuje, tak jej před vložením smažeme
										if (btn) editor.dom.remove(editor.selection.getNode().closest('.btn'))
										editor.insertContent(newBtn);
										api.close();
									},
								});
							}
							editor.ui.registry.addToggleButton('skButtonWindow', {
								text: 'Tlačítko',
								tooltip: 'Tlačítko',
								onAction: skButtonWindow,
								onSetup: (buttonApi) => editor.selection.selectorChangedWithUnbind('a.btn', buttonApi.setActive).unbind,
							});

							// 'pre', 'p', 'code', 'h1', 'h5', 'h6'
							[ 'h2', 'h3', 'h4', 'code'].forEach(function(name) {
								editor.ui.registry.addButton("style-" + name, {
									tooltip: "Toggle " + name,
									text: name.toUpperCase(),
									onAction: function() { editor.execCommand('mceToggleFormat', false, name); },
									onPostRender: function() {
										var self = this, setup = function() {
											editor.formatter.formatChanged(name, function(state) {
												self.active(state);
											});
										};
										editor.formatter ? setup() : editor.on('init', setup);
									}
								})
							});
						},

						// force_br_newlines : false,
						// force_p_newlines : false,
						// forced_root_block : '',

						valid_elements : "@[id|class|style|title|dir<ltr?rtl|lang|xml::lang|onclick|ondblclick|"
						+ "onmousedown|onmouseup|onmouseover|onmousemove|onmouseout|onkeypress|"
						+ "onkeydown|onkeyup],a[rel|rev|charset|hreflang|tabindex|accesskey|type|"
						+ "name|href|target|title|class|onfocus|onblur],strong/b,em/i,strike,u,"
						+ "#p,-ol[type|compact],-ul[type|compact],-li,br,img[longdesc|usemap|"
						+ "src|border|alt=|title|hspace|vspace|width|height|align|loading=lazy],-sub,-sup,"
						+ "-blockquote,-table[border=0|cellspacing|cellpadding|width|frame|rules|"
						+ "height|align|summary|bgcolor|background|bordercolor],-tr[rowspan|width|"
						+ "height|align|valign|bgcolor|background|bordercolor],tbody,thead,tfoot,"
						+ "#td[colspan|rowspan|width|height|align|valign|bgcolor|background|bordercolor"
						+ "|scope],#th[colspan|rowspan|width|height|align|valign|scope],caption,-div,"
						+ "-span,-code,-pre,address,-h2,-h3,-h4,-h5,-h6,hr[size|noshade],dd,dl,dt,cite,abbr,acronym,del[datetime|cite],ins[datetime|cite],"
						+ "object[classid|width|height|codebase|*],param[name|value|_value],embed[type|width"
						+ "|height|src|*],script[src|type],map[name],area[shape|coords|href|alt|target],bdo,"
						+ "button,col[align|char|charoff|span|valign|width],colgroup[align|char|charoff|span|"
						+ "valign|width],dfn,fieldset,form[action|accept|accept-charset|enctype|method],"
						+ "input[accept|alt|checked|disabled|maxlength|name|readonly|size|src|type|value],"
						+ "kbd,label[for],legend,noscript,optgroup[label|disabled],option[disabled|label|selected|value],"
						+ "q[cite],samp,select[disabled|multiple|name|size],small,figure,figcaption,"
						+ "textarea[cols|rows|disabled|name|readonly],tt,var,big,iframe[src|frameborder|allowfullscreen|width|height]",
						// codemirror: {
						// 	indentOnInit: true, // Whether or not to indent code on init.
						// 	path: 'CodeMirror', // Path to CodeMirror distribution
						// 	config: {           // CodeMirror config object
						// 		lineNumbers: true
						// 	}
						// },
						plugins: [ // stylebuttons // tinydrive // TODO codemirror
							"image wordcount link anchor table tabfocus paste media fullscreen charmap nonbreaking visualblocks lists advlist code"
						],
						// plugins: 'code print preview fullpage searchreplace autolink directionality  visualblocks visualchars fullscreen image link media  codesample table charmap hr pagebreak nonbreaking anchor toc insertdatetime advlist lists wordcount  imagetools textpattern help',
						// image_list: [
						// 	{ title: 'My page 1', value: 'http://www.tinymce.com' },
						// 	{ title: 'My page 2', value: 'http://www.moxiecode.com' }
						// ],
						// image_class_list: [
						// 	{ title: 'None', value: '' },
						// 	{ title: 'Some class', value: 'class-name' }
						// ],
						// file_picker_callback: function (callback, value, meta) {
						// 	if (meta.filetype === 'image') {
						// 		callback('https://www.google.com/logos/google.jpg', { alt: 'My alt text' });
						// 	}
						// },

						file_picker_callback: function (callback, value, meta) {
							var successHandler = function(event) {
								$('.skbox-window:visible').on('click', '.thumb', function(event) {
									var $img = $(this).find('img');
									var $name = $(this).find('.name');

									callback($img.data('src'), { alt: $name.text() });
									$('.skbox-window:visible .skbox-close').trigger('click');
								});
								$(thickbox).off('ajaxsuccess.skbox', successHandler);
							}

							if (meta.filetype === 'image') {
								$(thickbox).on('ajaxsuccess.skbox', successHandler);
								var $trigger = $('#js-media-library-trigger');
								$trigger.trigger('click');
							}
						},

						template_cdate_format: '[CDATE: %m/%d/%Y : %H:%M:%S]',
						template_mdate_format: '[MDATE: %m/%d/%Y : %H:%M:%S]',

						// api_key: "jlzoyubearrz1avd2qg7258svhy9n3q856unzj63lr4rjpo5",

						// image_list: function(success) {
						// 	success([
						// 		{title: 'My image 1', value: 'https://www.tinymce.com/my1.gif'},
						// 		{title: 'My image 2', value: 'http://www.moxiecode.com/my2.gif'}
						//
						// 		// {
						// 		// 	path: "/Hero Backgrounds",
						// 		// 	name: "action-adventure-bicycle-71104.jpg",
						// 		// 	url: "/images/drive-demo/Hero Backgrounds/action-adventure-bicycle-71104.jpg",
						// 		// 	date: "2018-06-13 16:49:10",
						// 		// 	size: 756767,
						// 		// 	thumbUrl: "/images/drive-demo/Hero Backgrounds/action-adventure-bicycle-71104_thumb.jpg
						// 		// },
						//
						// 	]);
						// },

						language : "cs",
					};


					// dafault wysiwyg
					var tinyMceDefaultConfig = tinyMceConfig;
					tinyMceDefaultConfig.formats = {
						alignleft: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-text-left' },
						aligncenter: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-text-center' },
						alignright: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-text-right' }
					};
					tinyMceDefaultConfig.content_css = '/static/css/wysiwyg.css';
					tinyMceDefaultConfig.toolbar = "undo redo | style-h2 style-h3 style-h4 | bold italic strikethrough | alignleft aligncenter alignright | bullist numlist outdent indent | image | link unlink anchor | nonbreaking hr charmap | skButtonWindow | code";

					$('.wysiwyg', event.target).tinymce(tinyMceDefaultConfig);

					//emailTemplate wysiwyg
					var tinyMceEmailTempalteConfig = tinyMceDefaultConfig;
					tinyMceEmailTempalteConfig.convert_urls = false;
					$('.emailTemplateWysiwag', event.target).tinymce(tinyMceEmailTempalteConfig);



					$('.file_upload', event.target).each(function(){

						var $uploadifiveInstance = $(this);
						var data = $uploadifiveInstance.data();
						$uploadifiveInstance.uploadifive({
							'auto'             : true,
							'buttonText'             : '',
							'removeCompleted': false,
							'formData'         : {
								'upSessionID' : app.options.sessionID,
								'id': data.idref,
								'paramValue': data.paramvalue
							},
							'queueID'          : data.place,
							'multi'                 : true,
							'uploadScript'     : data.script,
							'itemTemplate': data.pattern || '',
							'onProgress'   : function(file, e) {
								if (e.lengthComputable) {
									var percent = Math.round((e.loaded / e.total) * 100);
									file.queueItem.find('.uploadify-progress-bar').css('width', percent+'%')
								}
							},

							// zakomentovat nasledujici metodu, pokud ma byt obrazek na konci seznamu
							'onAddQueueItem': function(file)
							{
								var $target = $('#'+ data.place);

								file.queueItem.detach();
								$target.prepend(file.queueItem);
							},

							'onUploadComplete': function(file, newItem)
							{
								var $newItem = $(newItem);
								var $img = $newItem.find('img');

								var replace = function()
								{
									// replace
									file.queueItem.replaceWith( $newItem )

									// target
									var $target = $('#'+ data.place);
									$target.trigger('contentload');

									// Show target
									if( $target.find('li').length && $target.is(':hidden') )
									{
										$target.closest('.holder').slideDown(250);
									}
								};

								if( $img.length )
								{
									$newItem
										.addClass('loading')

									$img
										.on('load', function(event)
										{
											replace();
											$img.fadeTo(250, 1)
										});
								}
								else
								{
									replace();
								}

							}
						});
					})

					// uploadifyadify init
					/*
					$('.file_upload', event.target).each(function(){

						var $uploadifyInstance = $(this);
						var data = $uploadifyInstance.data();

						$uploadifyInstance.uploadify({
							// debug: true,
							swf: '/admin/js/uploadify/uploadify.swf',
							uploader: data.script,
							place: data.place,
							//buttonClass: 'btn btn-icon-before',
							buttonText: '', //$uploadifyInstance.data('button-text'),
							//height: '30',
							//width: '180',
							queueID: data.place,
							itemTemplate: data.pattern || '',
							queueMethod: data.method ? data.method : 'append',
							removeCompleted: false,
							formData: {
								'upSessionID' : app.options.sessionID,
								'id': data.idref
							},
							removeTimeout: 0.5,
							onUploadSuccess: function(file, data, response)
							{
								var _this = this;
								var $newItem = $(data);
								var $img = $newItem.find('img');

								var replace = function()
								{
									// replace
									$('#'+file.id).replaceWith( $newItem )

									// target
									var $target = $('#'+ _this.settings.place);
									$target.trigger('contentload');

									// Show target
									if( $target.find('li').length && $target.is(':hidden') )
									{
										$target.closest('.holder').slideDown(250);
									}
								};

								if( $img.length )
								{
									$newItem
										.addClass('loading')

									$img
										.on('load', function(event)
										{
											replace();
											$img.fadeTo(250, 1)
										});
								}
								else
								{
									replace();
								}

							}
						});
					})
					*/

					$('#frm-editForm-type', event.target)
						.on('change', function(event)
						{
							if ($("#frm-editForm-type").val() == "amount") {
								$('.percent').hide();
								$('.price').show();
							} else {
								$('.percent').show();
								$('.price').hide();
							}
						});


					// Delete config
					$('.btn-delete', event.target)
						.on('click', function(event)
						{
							event.stopPropagation();

							var msgAppend = $(this).data('msg') ? ' položku ' + $(this).data('msg') : '';

							if (app.confirm('Opravdu smazat'+msgAppend+'?'))
							{
								return true;
							}
							else
							{
								event.stopImmediatePropagation();
								return false;
							}
						});


					$('.btn-warning', event.target)
						.on('click', function(event)
						{
							event.stopPropagation();

							if (app.confirm('Opravdu chcete tuto akci provést?'))
							{
								return true;
							}
							else
							{
								event.stopImmediatePropagation();
								return false;
							}
						});

					$('.js-remove', event.target)
					.off('click.remove')
					.on('click.remove', function(event) {
						event.preventDefault();
						var $this = $(this).closest('.js-li');
						var $wrap = $this.closest('.js-ul');
						var $items = $this.nextAll();
						var $all = $this.add($items);

						$this.remove();

						// $this.css('overflow', 'hidden');
						// $all.css('position', 'relative');
						//
						// var done = function() {
						// 	$this.remove();
						// 	$items.css({'top': '', 'left': ''});
						// 	// remove position
						// 	$all.css('position', '');
						// };
						// $wrap.closest('.holder').slideUp(250, done);
					});


					$('.js-question-type', event.target).on('change',function(event) {
						var $listTargetToHide = $(this).closest('.js-li').find('.js-ul');
						var $buttonTargetToHide = $(this).closest('.js-li').find('.js-add-button');


						if (this.value == 'text') {
							$listTargetToHide.hide('slow');
							$buttonTargetToHide.hide('slow');
						} else {
							$listTargetToHide.show('slow');
							$buttonTargetToHide.show('slow');
						}
					})


					// Remove attached items
					$('.remove', event.target)
						.off('click.remove')
						.on('click.remove', function(event)
						{
							event.preventDefault();
							event.stopPropagation();

							// Deleting images
							if( $(this).closest('.delete-images').length )
							{
								var href = $(this).attr('href');

								if(href && href.search('#') !== 0)
								{
									$.ajax({
										url: href,
										type: 'GET',
										dataType: 'text'
									})
								}

								//return;
							}

							var undefined;
							var liTarget = 'li';
							var ulTarget = 'ul';

							if ($(this).data('litarget') != undefined) {
								liTarget = $(this).data('litarget');
							}

							if ($(this).data('ultarget') != undefined) {
								ulTarget = $(this).data('ultarget');
							}
							var $this = $(this).closest(liTarget);
							var $wrap = $this.closest(ulTarget);

							var $items = $this.nextAll();
							var $all = $this.add($items);

							var isImages = $this.closest('.crossroad-images').length;
							// obrazky u hodnot parametru
							if (!isImages) {
								isImages = $this.closest('.crossroad-images-param-values').length;
							}

							// start opacity and height
							var startPos = { o: 1, height: $this.height()  };
							var endPos = { o: 0, height: 1 };
							if(isImages)
							{
								// close grid
								$all.find('>.inner').filter('.active').find('.thumb').trigger('click', [true]);

								// get anim position
								$all.each(function(i)
								{
									var pos = $(this).position();

									if(i > 0)
									{
										startPos['y'+(i-1)] = pos.top;
										startPos['x'+(i-1)] = pos.left;
									}
									if(i < $items.length)
									{
										endPos['y'+(i)] = pos.top;
										endPos['x'+(i)] = pos.left;
									}
								});
							}

							// Set position
							$this.css('overflow', 'hidden');
							$all.css('position', 'relative');

							// Done
							var done = function()
							{
								$this.remove();
								$items.css({'top': '', 'left': ''});

								// remove position
								$all.css('position', '');
							}



							if ($(this).data('animdisabled') != undefined) {
								$this.remove();
								return;
							}

							if ($wrap.children().length <= 1 && !isImages && $wrap.closest('.holder').length)
							{
								$wrap.closest('.holder').slideUp(250, done);
							}
							else
							{
								console.log('anim')
								// anim new position
								$.Animation($.extend({}, startPos), endPos, { duration: 250, easing: 'swing' })
									.progress(function(obj)
									{

										$this.css({'opacity': obj.elem.o, 'height': obj.elem.height });

										if(isImages)
										{
											$items.each(function(i)
											{
												// diff actual position vs start position
												$(this).css({'top': ( obj.elem['y'+i] - startPos['y'+i]  ), 'left': ( obj.elem['x'+i] - startPos['x'+i] ) });
											});
										}
									})
									.done(done);
							}
						});


					// Fixed bar
					$('.fixed-bar:visible', event.target)
						.each(function()
						{
							$(this)/*.appendTo('#main')*/.before('<div class="fixed-bar-placeholder" style="height:'+ $(this).outerHeight(false) +'px" />');
						});

					// Beautify OL
					$('ol[start]', event.target)
						.css('counter-reset', function()
						{
							 return 'item ' + ( $(this).prop('start') - 1 )
						})

					// ie 7 OL
					$('html.ie7 ol', event.target)
						.each(function(i)
						{
							var start = $(this).prop('start') * 1;

							$(this).find('li')
								.each(function(i)
								{
									$(this).prepend('<span class="ie-counter">' + (start + i) + '.</span>');
								});
						});

					if (isCreate)
					{
						$('.col-content input:text:first').one('focus', function(){this.select()}).focus();
					}

					// suggest
					$('.inp-suggest', event.target).each(function(i){
						var $this = $(this),
							$parent = $this.parent(),
							$hidden = $parent.find('input:hidden');

						var Suggest = new sk.widgets.Suggest($this, {
							minLength: 2,
							typeInterval: 250,
							url: $this.data('suggest')
						});

						var $menu = $('<div class="suggest"></div>').appendTo( $parent );

						var SuggestMenu = new sk.widgets.SuggestMenu($menu, Suggest, {
							'item': '.item'
						}).init();

						$(SuggestMenu)
							.on('menuselect', function(){
								var $item = this.$items.eq( this.selectedIndex-1 );
								$hidden.val( $item.data('id') )
								$this.val( $item.text() )
							})
					});

					// suggest global search
					$('.inp-suggest-global', event.target).each(function(i){
						var $this = $(this),
							$parent = $this.parent(),
							$hidden = $parent.find('input:hidden');

						var Suggest = new sk.widgets.Suggest($this, {
							minLength: 2,
							typeInterval: 250,
							url: $this.data('suggest')
						});

						var $menu = $('<div class="suggest"></div>').appendTo( $parent );

						var SuggestMenu = new sk.widgets.SuggestMenu($menu, Suggest, {
							'item': '.item'
						}).init();

						$(SuggestMenu)
							.on('menuselect', function(){
								var $item = this.$items.eq( this.selectedIndex-1 );
								window.location.href = $item.find('a').attr('href');
							})
					});

					$('#search', event.target).on('click', '.search-trigger', function(){
						var $search = $('#search');
						var $input = $search.find('input');
						var $suggest = $search.find('.suggest');

						$search .toggleClass('is-opened');
						if ($search.hasClass('is-opened')) {
							$input.get(0).focus();
						} else {
							$input.val('');
							$suggest.html('');
						}
					});

				})
				.trigger('contentload')

				 $.datepicker.regional['cs'] = {
			        closeText: 'Zavřít',
			        prevText: '&#x3c;Dříve',
			        nextText: 'Později&#x3e;',
			        currentText: 'Nyní',
			        monthNames: ['leden', 'únor', 'březen', 'duben', 'květen', 'červen', 'červenec', 'srpen',
			            'září', 'říjen', 'listopad', 'prosinec'],
			        monthNamesShort: ['led', 'úno', 'bře', 'dub', 'kvě', 'čer', 'čvc', 'srp', 'zář', 'říj', 'lis', 'pro'],
			        dayNames: ['neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota'],
			        dayNamesShort: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
			        dayNamesMin: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
			        weekHeader: 'Týd',
			        dateFormat: 'dd/mm/yy',
			        firstDay: 1,
			        isRTL: false,
			        showMonthAfterYear: false,
			        yearSuffix: ''
			    };
			    $.datepicker.setDefaults($.datepicker.regional['cs']);
		}
	};


	// Grid
	$.fn.grid = function()
	{
		return this.each(function()
		{
			var $activeParent = null;
			var $activeDetail = null;
			var anim = null;
			var duration = 250;

			$(this)
				.off('click.skgrid')
				.on('click.skgrid', 'li .thumb', function(e, quick)
				{

					var $this = $(this);
					var $detail = $this.next('.detail');
					var $parent = $this.parent();
					var duration = quick ? 0 : duration;

					if( $detail.length )
					{
						e.preventDefault();

						if(anim)
						{
							anim.stop(true);
							anim = null;
						}

						var isActive = $parent.hasClass('active');
						var height = $detail.prop('scrollHeight');
						var start = $detail.height();
						var end = isActive ? 0 : height;

						if( !$activeParent || isActive )
						{
							anim = $.Animation({
								comming: start
							},{
								comming: end
							},{
								duration: duration,
								easing: 'swing'
							})
							.progress(function(obj)
							{
								$parent.css('padding-bottom', obj.elem.comming);
								$detail.height( obj.elem.comming );
							})
							.done(function()
							{
								isActive && $parent.removeClass('active');
							});
						}
						else if( $activeParent.position().top === $parent.position().top )
						{
							$activeParent.css('padding-bottom', start).removeClass('active');
							$activeDetail.css('height', start);

							$parent.css('padding-bottom', end);
							$detail.height( end );

							isActive = false;
						}
						else
						{
							var $oldParent = $activeParent;
							var $oldDetail = $activeDetail;

							anim = $.Animation({
								current: $activeDetail.height(),
								comming: start
							},{
								current: 0,
								comming: end
							},{
								duration: duration,
								easing: 'swing'
							})
							.progress(function(obj)
							{
								$oldParent.css('padding-bottom', obj.elem.current);
								$oldDetail.height( obj.elem.current );

								$parent.css('padding-bottom', obj.elem.comming);
								$detail.height( obj.elem.comming );
							})
							.done(function()
							{
								$oldParent.removeClass('active')
							})

							isActive = false;
						}

						if(!isActive)
						{
							//$detail.find('.timeline').trigger("update.timeline");
						}

						$activeParent = isActive ? null : $parent;
						$activeDetail = isActive ? null : $detail;

						!isActive && $parent.addClass('active');
					}
				});
		});
	}
})(jQuery)
