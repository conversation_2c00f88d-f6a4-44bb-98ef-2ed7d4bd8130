image:
  name: 368257470444.dkr.ecr.eu-central-1.amazonaws.com/php:8.2
  aws:
    access-key: $AWS_ECR_ACCESS_KEY_ID
    secret-key: $AWS_ECR_SECRET_ACCESS_KEY

definitions:
  caches:
    node-new-admin: src/admin/new/node_modules
  services:
    mysql:
      image: mariadb:10
      variables:
        MYSQL_ROOT_PASSWORD: root
        MYSQL_DATABASE: superadmin2019
    redis:
      image: redis:latest

pipelines:
  default:
    - step: &composer-install
        name: Install dependencies
        script:
          - php composer.phar install
        caches:
          - composer
        artifacts:
          - vendor/**

    - parallel: &quality-control
        - step:
            name: PHP Linter
            script:
              - php composer.phar run-script lint

        - step:
            name: Latte <PERSON>
            script:
              - php composer.phar run-script latte-lint

        - step:
            name: Static Analysis
            script:
              - php composer.phar run-script phpstan

        - step:
            name: Tests
            services:
              - mysql
              - redis
            script:
              - cp tests/config.ci.neon app/config/config.local.neon
              - php bin/console migrations:continue
              - php composer.phar run-script tests

  branches:
    master:
      - step: *composer-install
      - parallel: *quality-control

      - parallel: &prepare
        - step:
            name: Install production dependencies
            script:
              - php composer.phar install --no-dev --classmap-authoritative
            caches:
              - composer
            artifacts:
              download: false
              paths:
                - vendor/**

        - step:
            name: Build front-end
            image: node:16
            script:
              - npm install
              - npm run build
            caches:
              - node
            artifacts:
              - www/static/**

        - step:
            name: Build new admin
            image: node:14
            script:
              - cd src/admin/new
              - npm install
              - npm run build
            caches:
              - node-new-admin
            artifacts:
              - www/admin/new/dist/**

      - step:
          name: Deploy to stage
          image:
            name: 368257470444.dkr.ecr.eu-central-1.amazonaws.com/docker-php-deployer:v7.0.0-rc.8
            aws:
              access-key: $AWS_ECR_ACCESS_KEY_ID
              secret-key: $AWS_ECR_SECRET_ACCESS_KEY
          deployment: stage
          script:
            # prepare SSH key
            - mkdir -p ~/.ssh
            - (umask 077; echo $DEPLOYMENT_KEY | base64 -d > ~/.ssh/id_ed25519)

            # mark version
            - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $BITBUCKET_COMMIT > app/config/webVersion.neon'

            # run deployer
            - dep -f .deploy.php deploy

  tags:
    'prod-*':
      - step: *composer-install
      - parallel: *quality-control
      - parallel: *prepare

      - step:
          name: Deploy to production
          deployment: production
          image:
            name: 368257470444.dkr.ecr.eu-central-1.amazonaws.com/docker-php-deployer:v7.0.0-rc.8
            aws:
              access-key: $AWS_ECR_ACCESS_KEY_ID
              secret-key: $AWS_ECR_SECRET_ACCESS_KEY
          script:
            - mkdir -p ~/.ssh
            - (umask 077; echo $DEPLOYMENT_KEY | base64 -d > ~/.ssh/id_ed25519)
            - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $BITBUCKET_TAG > app/config/webVersion.neon'
            - dep -f .deploy.php deploy

  custom:
    dev:
      - variables:
          - name: resetDataStorage

      - step: *composer-install
      - parallel: *quality-control
      - parallel: *prepare

      - step:
          name: Deploy to stage
          deployment: stage
          image:
            name: 368257470444.dkr.ecr.eu-central-1.amazonaws.com/docker-php-deployer:v7.0.0-rc.8
            aws:
              access-key: $AWS_ECR_ACCESS_KEY_ID
              secret-key: $AWS_ECR_SECRET_ACCESS_KEY
          script:
            - mkdir -p ~/.ssh
            - (umask 077; echo $DEPLOYMENT_KEY | base64 -d > ~/.ssh/id_ed25519)
            - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $BITBUCKET_COMMIT > app/config/webVersion.neon'
            - dep -f .deploy.php deploy
