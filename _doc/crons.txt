#sync product
0 8-20 * * * flock -n /tmp/u15339_sync_product php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:import:product && php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:process:product -c

#RTS
15 8-20 * * * flock -n /tmp/u15339_sync_rts php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:import:rts && php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:process:rts -c
45 8-20 * * * flock -n /tmp/u15339_sync_rts_watchdog php8.2 /chroot/u15339/home/<USER>/app/current/bin/console rts:watchdog

#sync documents
0 6-22 * * * flock -n /tmp/u15339_sync_doc php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:import:documentGroupProduct && php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:process:documentGroupProduct && php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:import:documentGroup && php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:process:documentGroup && php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:import:document && php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:process:document -c

*/5 * * * * flock -n /tmp/u15339_sync_check_doc php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sync:check:document

#configuration order
* * * * * php8.2 /chroot/u15339/home/<USER>/app/current/bin/console configuration:order:create
*/5 * * * * php8.2 /chroot/u15339/home/<USER>/app/current/bin/console configuration:order:check

# configurator parameters
*/10 * * * * php8.2 /chroot/u15339/home/<USER>/app/current/bin/console configurator:parameter:copy

#sitemap
45 7-21 * * * flock -n /tmp/u15339_sitemap php8.2 /chroot/u15339/home/<USER>/app/current/bin/console sitemap

#feed
*/10 8-20 * * * flock -n /tmp/u15339_reseller php8.2 /chroot/u15339/home/<USER>/app/current/bin/console product:feed:reseller
8,18,28,38,48,58 8-20 * * * flock -n /tmp/u15339_reseller php8.2 /chroot/u15339/home/<USER>/app/current/bin/console product:feed:rts
