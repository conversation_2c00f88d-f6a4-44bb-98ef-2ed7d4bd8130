Setup:
	Registrace
	/.config/systemd/user$ systemctl --user enable ton_stage_elasticFront_consume.service

	Spuštení
	$ systemctl --user start ton_stage_elasticFront_consume.service

	Sezanm aktivních
    $ systemctl --user list-units *


Content of file:

[Unit]
Description=Service for consuming 'elasticFront' queue for production
StartLimitIntervalSec=0

[Service]
Type=simple
Restart=always
RestartSec=1
ExecStart=php8.0 /chroot/u15149/home/<USER>/app/current/bin/console messenger:consume elasticFront --limit=200

[Install]
WantedBy=default.target
