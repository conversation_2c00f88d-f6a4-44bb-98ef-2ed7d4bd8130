networks:
  ton:
  superkoders:
      external: true

volumes:
  db:
  es:

services:
  app:
    build:
      dockerfile: docker/app/Dockerfile
      context: .
    hostname: app
    container_name: ton_app
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - ton
      - superkoders
    labels:
        - "traefik.enable=true"
        - "traefik.docker.network=superkoders"
        - "traefik.http.routers.ton.rule=Host(`ton.superkoders.test`)"
        - "traefik.http.routers.ton.tls=true"
    volumes:
      - .:/var/www/html
      - ./docker/app/php-xdebug-${SUPERADMIN_XDEBUG:-off}.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - db
      - es
      - front
      - admin

  front:
    build:
      dockerfile: docker/front/Dockerfile
      context: .
    container_name: ton_front
    restart: unless-stopped
    networks:
      - ton
    volumes:
      - .:/app

  admin:
    build:
      dockerfile: docker/admin/Dockerfile
      context: .
    container_name: ton_admin
    restart: unless-stopped
    networks:
      - ton
    volumes:
      - .:/app

  adminer:
    image: adminer
    restart: always
    networks:
      - ton
    ports:
      - "81:8080"

  db:
    image: mariadb:10
    hostname: ton_db
    container_name: ton_db
    restart: unless-stopped
    networks:
      - ton
    ports:
      - "3306:3306"
    volumes:
        - db:/var/lib/mysql
        - ./docker/db:/docker/db
        - ./docker/db/compare-db.sh:/docker/db/compare-db.sh:ro
    environment:
      MARIADB_ROOT_PASSWORD: 'root'
      MARIADB_DATABASE: 'ton'
      MARIADB_USER: 'ton'
      MARIADB_PASSWORD: 'ton'

  db-init:
      image: mariadb:10
      container_name: ton_db-init
      depends_on:
          - db
      networks:
          - ton
      volumes:
          - ./docker/db/init-db.sh:/docker/db/init-db.sh:ro
          - ./docker/db:/docker/db
      environment:
          MARIADB_HOST: 'ton_db'
          MARIADB_ROOT_PASSWORD: 'root'
          MARIADB_DATABASE: 'ton'
          MARIADB_USER: 'ton'
          MARIADB_PASSWORD: 'ton'
      entrypoint: [ "/docker/db/init-db.sh" ]

  es:
    image: elasticsearch:7.17.6
    hostname: ton_es
    container_name: ton_es
    restart: unless-stopped
    networks:
      - ton
    ports:
      - "9200:9200"
    volumes:
      - es:/usr/share/elasticsearch/data
    environment:
      "discovery.type": single-node

  redis:
    image: redis:latest
    hostname: ton_redis
    container_name: ton_redis
    restart: unless-stopped
    networks:
      - ton
    ports:
      - "6379:6379"

  mailcatcher:
    image: dockage/mailcatcher
    hostname: ton_mailcatcher
    container_name: ton_mailcatcher
    restart: unless-stopped
    networks:
      - ton
    ports:
      - "1080:1080"
